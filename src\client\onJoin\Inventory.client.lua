-- Custom Tool Inventory System
-- Handles both task bar (hotbar) and full inventory interface for tools
-- Maximum 8 tools supported (hotkeys 1-8)

local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local StarterGui = game:GetService("StarterGui")

local player = Players.LocalPlayer

-- Mobile detection function
local function isMobileDevice()
	return UserInputService.TouchEnabled and not UserInputService.KeyboardEnabled
end

-- Disable default Roblox backpack and toolbar
StarterGui:SetCoreGuiEnabled(Enum.CoreGuiType.Backpack, false)

-- Wait for PlayerGui and get UI references
local playerGui = player:WaitForChild("PlayerGui")
local InventoryUI = playerGui:WaitForChild("Inventory")
local ToolBar = InventoryUI:WaitForChild("ToolBar")
-- Inventory frame removed - only using toolbar now
local Inventory = nil -- Set to nil since inventory frame was removed
local Template = ReplicatedStorage:WaitForChild("ToolBarTemplate")
local TemplateBtn = Template:WaitForChild("ToolBtn")
local TemplateImage = Template:FindFirstChild("ImageLabel")
local ToolNumber = Template:WaitForChild("ToolNumber")
local stroke = Template:WaitForChild("UIStroke")

-- Tool Configuration with Categories (Maximum 8 tools supported)
local TOOL_CATEGORIES = {
    Core = {
        displayName = "Core Tools",
        description = "Essential building and editing tools",
        tools = {
            Blueprints = {
                name = "Blueprints",
                imageId = "", -- No image, will show tool name as text
                description = "Build and place objects",
                hotkey = "1"
            },
            Adjust = {
                name = "Adjust",
                imageId = "", -- No image, will show tool name as text
                description = "Remove and adjust objects",
                hotkey = "2"
            }
        }
    }
    -- Additional categories can be added here
    -- Example:
    -- Utilities = {
    --     displayName = "Utility Tools",
    --     description = "Helper and utility tools",
    --     tools = {
    --         ToolName3 = {
    --             name = "Tool Name",
    --             imageId = "rbxassetid://123456789",
    --             description = "Tool description",
    --             hotkey = "3"
    --         }
    --     }
    -- }
}

-- Flatten tool config for backwards compatibility
local TOOL_CONFIG = {}
for categoryName, category in pairs(TOOL_CATEGORIES) do
    for toolName, toolData in pairs(category.tools) do
        TOOL_CONFIG[toolName] = toolData
        TOOL_CONFIG[toolName].category = categoryName
    end
end



-- Constants
local HOTBAR_SIZE = 8 -- Number of slots in the hotbar (1,2,3,4,5,6,7,8)
local SELECTED_TRANSPARENCY = 0.06
local UNSELECTED_TRANSPARENCY = 0.65
local TWEEN_INFO = TweenInfo.new(0.15, Enum.EasingStyle.Quart, Enum.EasingDirection.Out)
local SCALE_FACTOR = 1.08 -- How much bigger the selected slot gets (8% larger)

-- State variables
local inventoryOpen = false
local selectedHotbarSlot = 0 -- Start with no selection
local hotbarSlots = {}
local currentTools = {}
local equippedTool = nil -- Track currently equipped tool name
local lastClickTime = 0 -- Prevent rapid clicking
local isUpdatingState = false -- Flag to prevent recursive state updates

-- Helper function to get tools by category
local function getToolsByCategory()
    local categorizedTools = {}
    -- Safety check: ensure currentTools is initialized
    if not currentTools then
        return categorizedTools
    end

    for _, toolData in ipairs(currentTools) do
        local category = toolData.category or "Uncategorized"
        if not categorizedTools[category] then
            categorizedTools[category] = {}
        end
        table.insert(categorizedTools[category], toolData)
    end
    return categorizedTools
end

-- Helper function to get category info
local function getCategoryInfo(categoryName)
    return TOOL_CATEGORIES[categoryName] or {
        displayName = categoryName,
        description = "Tools in " .. categoryName .. " category"
    }
end

-- Forward declare functions that are used before they're defined
local selectHotbarSlot
local equipTool
local toggleInventory

-- UI Styling functions
local function styleSelectedSlot(slot)
    if slot then
        -- Update stroke transparency - set immediately and tween for smoothness
        local uiStroke = slot:FindFirstChild("UIStroke")
        if uiStroke then
            uiStroke.Transparency = SELECTED_TRANSPARENCY -- Set immediately to ensure it shows
            local strokeTween = TweenService:Create(uiStroke, TWEEN_INFO, {Transparency = SELECTED_TRANSPARENCY})
            strokeTween:Play()
        end

        -- Scale up the slot from center when selected
        local originalSize = slot:GetAttribute("OriginalSize") or slot.Size
        local originalPosition = slot:GetAttribute("OriginalPosition") or slot.Position

        local scaledSize = UDim2.new(
            originalSize.X.Scale * SCALE_FACTOR,
            originalSize.X.Offset * SCALE_FACTOR,
            originalSize.Y.Scale * SCALE_FACTOR,
            originalSize.Y.Offset * SCALE_FACTOR
        )

        -- Create smooth scaling tween (maintain original position for center scaling)
        local scaleTween = TweenService:Create(slot, TWEEN_INFO, {
            Size = scaledSize,
            Position = originalPosition
        })
        scaleTween:Play()
    end
end

local function styleUnselectedSlot(slot)
    if slot then
        -- Update stroke transparency - set immediately and tween for smoothness
        local uiStroke = slot:FindFirstChild("UIStroke")
        if uiStroke then
            uiStroke.Transparency = UNSELECTED_TRANSPARENCY -- Set immediately
            local strokeTween = TweenService:Create(uiStroke, TWEEN_INFO, {Transparency = UNSELECTED_TRANSPARENCY})
            strokeTween:Play()
        end

        -- Scale back to original size when unselected
        local originalSize = slot:GetAttribute("OriginalSize") or slot.Size
        local originalPosition = slot:GetAttribute("OriginalPosition") or slot.Position

        local scaleTween = TweenService:Create(slot, TWEEN_INFO, {
            Size = originalSize,
            Position = originalPosition
        })
        scaleTween:Play()
    end
end

-- Tool Management Functions
local function createHotbarSlot(index)
    local slot = Template:Clone()
    slot.Name = "HotbarSlot" .. index
    slot.Visible = false -- Initially hidden until tools are available

    -- Set anchor point to center BEFORE adding to parent to avoid position shifts
    slot.AnchorPoint = Vector2.new(0.5, 0.5)

    -- Now add to parent
    slot.Parent = ToolBar

    -- Store original size and position after anchoring is set
    local originalSize = slot.Size
    local originalPosition = slot.Position
    slot:SetAttribute("OriginalSize", originalSize)
    slot:SetAttribute("OriginalPosition", originalPosition)

    -- Hide tool number on mobile devices
    local toolNumber = slot:FindFirstChild("ToolNumber")
    if toolNumber and isMobileDevice() then
        toolNumber.Visible = false
    end

    -- Set initial styling
    styleUnselectedSlot(slot)

    -- Add click functionality - check for both possible button names
    local templateBtn = slot:FindFirstChild("TemplateBtn") or slot:FindFirstChild("ToolBtn")
    if templateBtn then
        templateBtn.MouseButton1Click:Connect(function()
            selectHotbarSlot(index)
        end)
    else
        -- Print all children to debug
        for _, child in pairs(slot:GetChildren()) do
        end
    end

    return slot
end

-- Unified function to update both visual and internal state
local function updateToolState(newSelectedSlot, newEquippedTool)
    if isUpdatingState then return end -- Prevent recursive calls
    isUpdatingState = true

    -- Update internal state
    selectedHotbarSlot = newSelectedSlot or 0
    equippedTool = newEquippedTool

    -- Update visual state for all slots
    for i, slot in pairs(hotbarSlots) do
        if i == selectedHotbarSlot then
            styleSelectedSlot(slot)
        else
            styleUnselectedSlot(slot)
        end
    end

    isUpdatingState = false
end

-- Simple and efficient unequip function
local function unequipCurrentTool()
    local character = player.Character
    if not character then return false end

    local humanoid = character:FindFirstChild("Humanoid")
    if not humanoid then return false end

    -- Simply unequip all tools - this is the most reliable method
    humanoid:UnequipTools()
    return true
end

selectHotbarSlot = function(index)
    -- Prevent rapid clicking
    local currentTime = tick()
    if currentTime - lastClickTime < 0.15 then
        return
    end
    lastClickTime = currentTime

    -- Validate slot
    if index < 1 or index > #currentTools or not hotbarSlots[index] then
        return
    end

    local toolData = currentTools[index]
    if not toolData then return end

    -- If clicking the same selected slot, unequip
    if selectedHotbarSlot == index then
        if unequipCurrentTool() then
            updateToolState(0, nil) -- Clear selection
        end
        return
    end

    -- Otherwise, equip the tool in this slot
    updateToolState(index, toolData.name) -- Update visual state immediately
    equipTool(toolData.name)
end

equipTool = function(toolName)
    local backpack = player:FindFirstChild("Backpack")
    local character = player.Character

    if not backpack or not character then
        updateToolState(0, nil) -- Reset state if can't equip
        return
    end

    local humanoid = character:FindFirstChild("Humanoid")
    if not humanoid then
        updateToolState(0, nil) -- Reset state if can't equip
        return
    end

    -- Unequip current tool first
    humanoid:UnequipTools()

    -- Find and equip the requested tool
    local tool = backpack:FindFirstChild(toolName)
    if tool and tool:IsA("Tool") then
        humanoid:EquipTool(tool)
        -- Note: equippedTool state will be updated by monitoring system
    else
        updateToolState(0, nil) -- Reset state if tool not found
    end
end

-- Helper function to get the actual tool's TextureId
local function getToolTextureId(toolName)
    local backpack = player:FindFirstChild("Backpack")
    local character = player.Character

    -- Check backpack first
    if backpack then
        local tool = backpack:FindFirstChild(toolName)
        if tool and tool:IsA("Tool") and tool.TextureId and tool.TextureId ~= "" then
            return tool.TextureId
        end
    end

    -- Check character if not in backpack
    if character then
        local tool = character:FindFirstChild(toolName)
        if tool and tool:IsA("Tool") and tool.TextureId and tool.TextureId ~= "" then
            return tool.TextureId
        end
    end

    return nil
end

local function updateHotbarSlot(index, toolData)
    local slot = hotbarSlots[index]
    if not slot or not toolData then return end

    -- Show the slot with tool data
    slot.Visible = true

    -- Handle image vs text display
    local imageLabel = slot:FindFirstChild("ImageLabel")
    local templateBtn = slot:FindFirstChild("TemplateBtn") or slot:FindFirstChild("ToolBtn")

    -- Get the actual tool's TextureId first, then fall back to config imageId
    local actualTextureId = getToolTextureId(toolData.name)
    local imageToUse = actualTextureId or toolData.imageId

    -- Check if we have a valid image to display
    local hasValidImage = imageToUse and imageToUse ~= "" and imageToUse ~= "rbxassetid://0"

    if hasValidImage then
        -- Tool has an image - show image, hide text
        if imageLabel then
            imageLabel.Image = imageToUse
            imageLabel.Visible = true
        end
        if templateBtn then
            templateBtn.Text = ""
        end
    else
        -- Tool has no image - show tool name as text, remove image
        if imageLabel then
            imageLabel:Destroy()
        end
        if templateBtn then
            templateBtn.Text = toolData.displayName or toolData.name
        end
    end

    -- Update hotkey number display (hide on mobile)
    local toolNumber = slot:FindFirstChild("ToolNumber")
    if toolNumber then
        if isMobileDevice() then
            toolNumber.Visible = false
        else
            toolNumber.Visible = true
            toolNumber.Text = toolData.hotkey or ""
        end
    end
end

local function updateToolBarVisibility()
    -- Show ToolBar if any tools are available AND plot objects are fully loaded, hide if none
    local hasTools = #currentTools > 0
    local plotReady = _G.plotObjectsFullyLoaded or false
    ToolBar.Visible = hasTools and plotReady

    if hasTools and not plotReady then
        print("Tools available but waiting for plot objects to load before showing toolbar")
    end
end

-- Tool Inventory Management Functions
local function refreshToolInventory()
    local backpack = player:FindFirstChild("Backpack")
    local character = player.Character

    currentTools = {}

    -- Collect available tools from both backpack and character
    for toolName, config in pairs(TOOL_CONFIG) do
        local toolInBackpack = backpack and backpack:FindFirstChild(toolName)
        local toolInCharacter = character and character:FindFirstChild(toolName)
        local actualTool = nil

        if toolInBackpack and toolInBackpack:IsA("Tool") then
            actualTool = toolInBackpack
        elseif toolInCharacter and toolInCharacter:IsA("Tool") then
            actualTool = toolInCharacter
        end

        if actualTool then
            -- Use the actual tool's TextureId if available, otherwise fall back to config
            local actualTextureId = actualTool.TextureId and actualTool.TextureId ~= "" and actualTool.TextureId or config.imageId

            table.insert(currentTools, {
                name = toolName,
                displayName = config.name,
                imageId = actualTextureId,
                description = config.description,
                hotkey = config.hotkey,
                category = config.category or "Uncategorized"
            })
        end
    end

    -- Sort tools by hotkey for consistency
    table.sort(currentTools, function(a, b)
        return (a.hotkey or "z") < (b.hotkey or "z")
    end)

    -- Enforce 8-tool limit
    if #currentTools > HOTBAR_SIZE then
        print("Warning: Player has more than", HOTBAR_SIZE, "tools. Only showing first", HOTBAR_SIZE)
        local limitedTools = {}
        for i = 1, HOTBAR_SIZE do
            limitedTools[i] = currentTools[i]
        end
        currentTools = limitedTools
    end

    -- Clear all existing hotbar slots
    for i = 1, #hotbarSlots do
        if hotbarSlots[i] then
            hotbarSlots[i]:Destroy()
            hotbarSlots[i] = nil
        end
    end
    hotbarSlots = {}

    -- Create slots only for tools the player has
    for i = 1, #currentTools do
        if i <= HOTBAR_SIZE then -- Don't exceed max hotbar size
            hotbarSlots[i] = createHotbarSlot(i)
            updateHotbarSlot(i, currentTools[i])
        end
    end

    -- Update toolbar visibility
    updateToolBarVisibility()

    -- Debug: Print tool categories
    if #currentTools > 0 then
        local categorizedTools = getToolsByCategory()

        print("=== Tool Inventory by Category ===")
        for categoryName, tools in pairs(categorizedTools) do
            local categoryInfo = getCategoryInfo(categoryName)
            print("Category:", categoryInfo.displayName, "(" .. categoryName .. ")")
            print("  Description:", categoryInfo.description)
            for _, toolData in ipairs(tools) do
                print("  - [" .. toolData.hotkey .. "]", toolData.displayName)
            end
        end
        print("===================================")
    end

    -- Check if currently equipped tool is still available
    local playerCharacter = player.Character
    local currentlyEquippedTool = nil
    if playerCharacter then
        for _, child in pairs(playerCharacter:GetChildren()) do
            if child:IsA("Tool") and TOOL_CONFIG[child.Name] then
                currentlyEquippedTool = child.Name
                break
            end
        end
    end

    -- Update state based on what's actually equipped
    if currentlyEquippedTool then
        -- Find the slot for the currently equipped tool
        for i, toolData in ipairs(currentTools) do
            if toolData.name == currentlyEquippedTool then
                updateToolState(i, currentlyEquippedTool)
                return
            end
        end
    end

    -- No tool equipped or equipped tool not in current tools
    updateToolState(0, nil)
end

-- Make refreshToolInventory globally available for plot loading system
_G.refreshToolInventory = refreshToolInventory

local function initializeHotbar()
    -- Clear existing hotbar slots
    for _, child in pairs(ToolBar:GetChildren()) do
        if child.Name:match("HotbarSlot") then
            child:Destroy()
        end
    end

    -- Initialize empty hotbar slots array
    hotbarSlots = {}

    -- Initial refresh will create slots as needed
    refreshToolInventory()
end

local function setupToolMonitoring()
    -- Monitor backpack for tool changes
    local function monitorBackpack()
        local backpack = player:FindFirstChild("Backpack")
        if not backpack then return end

        -- Monitor tools being added to backpack
        backpack.ChildAdded:Connect(function(child)
            if child:IsA("Tool") then
                print("Tool added to backpack:", child.Name)
                -- Add to TOOL_CONFIG if not already there
                if not TOOL_CONFIG[child.Name] then
                    local nextHotkey = tostring(#currentTools + 1)
                    if tonumber(nextHotkey) <= HOTBAR_SIZE then
                        -- Use the tool's actual TextureId if available
                        local toolTextureId = child.TextureId or ""
                        TOOL_CONFIG[child.Name] = {
                            name = child.Name,
                            imageId = toolTextureId,
                            description = "Tool: " .. child.Name,
                            hotkey = nextHotkey
                        }
                        print("Auto-added tool to config:", child.Name, "with hotkey", nextHotkey)
                    end
                end
                refreshToolInventory()
            end
        end)

        -- Monitor tools being removed from backpack
        backpack.ChildRemoved:Connect(function(child)
            if child:IsA("Tool") then
                print("Tool removed from backpack:", child.Name)
                -- Small delay to allow for tool equipping
                task.spawn(function()
                    task.wait(0.1)
                    refreshToolInventory()
                end)
            end
        end)

        -- Initial refresh
        refreshToolInventory()
    end

    -- Monitor character for tool equipping/unequipping
    local function monitorCharacter(character)
        if not character then return end

        -- Monitor tools being equipped
        character.ChildAdded:Connect(function(child)
            if child:IsA("Tool") and TOOL_CONFIG[child.Name] then
                print("Tool equipped via monitoring:", child.Name)

                -- Only update state if this wasn't triggered by our own equip action
                -- Check if the tool being equipped matches what we expect
                local expectedTool = currentTools[selectedHotbarSlot]
                if not expectedTool or expectedTool.name ~= child.Name then
                    -- This was an external equip (not from our UI), update to match
                    for i, toolData in ipairs(currentTools) do
                        if toolData.name == child.Name then
                            updateToolState(i, child.Name)
                            break
                        end
                    end
                else
                    -- This was from our UI, just update the equipped tool state
                    equippedTool = child.Name
                    print("Tool equipped matches expected, keeping current visual state")
                end
            end
        end)

        -- Monitor tools being unequipped
        character.ChildRemoved:Connect(function(child)
            if child:IsA("Tool") and TOOL_CONFIG[child.Name] then
                print("Tool unequipped via monitoring:", child.Name)

                -- Only clear selection if this tool was actually selected
                if equippedTool == child.Name then
                    updateToolState(0, nil) -- Clear selection
                end

                -- Refresh inventory after a brief delay to ensure toolbar stays visible
                task.spawn(function()
                    task.wait(0.1)
                    refreshToolInventory()
                end)
            end
        end)
    end

    -- Setup monitoring for current character
    if player.Character then
        monitorCharacter(player.Character)
    end

    -- Setup monitoring for future characters
    player.CharacterAdded:Connect(monitorCharacter)

    -- Setup backpack monitoring
    monitorBackpack()

    print("Tool monitoring setup complete")
end

-- Input Handling
local function setupInputHandling()
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end

        -- Handle number keys for tool selection (1-8 for available tools)
        if input.KeyCode == Enum.KeyCode.One then
            selectHotbarSlot(1)
        elseif input.KeyCode == Enum.KeyCode.Two then
            selectHotbarSlot(2)
        elseif input.KeyCode == Enum.KeyCode.Three then
            selectHotbarSlot(3)
        elseif input.KeyCode == Enum.KeyCode.Four then
            selectHotbarSlot(4)
        elseif input.KeyCode == Enum.KeyCode.Five then
            selectHotbarSlot(5)
        elseif input.KeyCode == Enum.KeyCode.Six then
            selectHotbarSlot(6)
        elseif input.KeyCode == Enum.KeyCode.Seven then
            selectHotbarSlot(7)
        elseif input.KeyCode == Enum.KeyCode.Eight then
            selectHotbarSlot(8)
        elseif input.KeyCode == Enum.KeyCode.Tab then
            toggleInventory()
        end
    end)
end

-- Full Inventory Interface Functions (disabled since inventory frame was removed)
toggleInventory = function()
    -- Inventory frame was removed, so just print a message
    print("Full inventory interface disabled - only toolbar available")
end

local function showFullInventory()
    -- Inventory frame was removed, do nothing
    print("Full inventory interface not available")
end

local function hideFullInventory()
    -- Inventory frame was removed, do nothing
end

local function populateFullInventory()
    -- Inventory frame was removed, do nothing
    print("Full inventory population skipped - frame not available")
end

-- Initialization
local function initializeToolInventorySystem()
    print("Initializing Custom Tool Inventory System...")

    -- Hide the full inventory initially
    if Inventory then
        Inventory.Visible = false
    end

    -- Hide the toolbar initially (will show when tools are available)
    if ToolBar then
        ToolBar.Visible = false
    end

    -- Initialize hotbar
    initializeHotbar()

    -- Setup input handling
    setupInputHandling()

    -- Setup tool monitoring
    task.spawn(function()
        setupToolMonitoring()
    end)

    print("Custom Tool Inventory System initialized!")
end

-- The tool inventory system handles tool selection and equipping
-- Functions are available through the UI interactions (hotbar clicks, keyboard shortcuts, etc.)

-- Initialize the system
initializeToolInventorySystem()

print("Custom Tool Inventory System loaded!")