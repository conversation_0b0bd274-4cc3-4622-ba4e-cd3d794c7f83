-- MessageHelper.lua
-- Helper module for easily displaying different types of messages from any client script

local ReplicatedStorage = game:GetService("ReplicatedStorage")

local MessageHelper = {}

-- Wait for the message events to be created
local showMessageEvent = ReplicatedStorage:WaitForChild("ShowMessageEvent", 10)
local showInfoMessageEvent = ReplicatedStorage:WaitForChild("ShowInfoMessageEvent", 10)
local showErrorMessageEvent = ReplicatedStorage:WaitForChild("ShowErrorMessageEvent", 10)
local clearMessagesEvent = ReplicatedStorage:WaitForChild("ClearMessagesEvent", 10)

-- Function to show a regular message (white text)
function MessageHelper.ShowMessage(text)
    if showMessageEvent then
        showMessageEvent:Fire(text)
    else
        warn("ShowMessageEvent not found - make sure Messages.client.lua is running")
    end
end

-- Function to show an info message (blue text)
function MessageHelper.ShowInfo(text)
    if showInfoMessageEvent then
        showInfoMessageEvent:Fire(text)
    else
        warn("ShowInfoMessageEvent not found - make sure Messages.client.lua is running")
    end
end

-- Function to show an error message (red text)
function MessageHelper.ShowError(text)
    if showErrorMessageEvent then
        showErrorMessageEvent:Fire(text)
    else
        warn("ShowErrorMessageEvent not found - make sure Messages.client.lua is running")
    end
end

-- Function to clear all messages
function MessageHelper.ClearAllMessages()
    if clearMessagesEvent then
        clearMessagesEvent:Fire()
    else
        warn("ClearMessagesEvent not found - make sure Messages.client.lua is running")
    end
end

return MessageHelper
