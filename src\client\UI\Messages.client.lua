local Players = game:GetService("Players")
local TweenService = game:GetService("TweenService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

print("DEBUG: Messages.client.lua starting...")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")
local infoUI = playerGui:WaitForChild("InfoUI")
local infoFrame = infoUI:WaitForChild("InfoFrame")

print("DEBUG: Messages.client.lua - Found InfoFrame:", infoFrame ~= nil)

-- Hide the original Message label since we'll create our own
local originalMessageLabel = infoFrame:FindFirstChild("Message")
if originalMessageLabel then
    originalMessageLabel.Visible = false
end

-- Hide InfoFrame by default
infoFrame.Visible = false

-- Message system configuration
local MESSAGE_DISPLAY_TIME = 3 -- seconds to display message
local TWEEN_TIME = 0.25 -- seconds for tween animations (faster for responsiveness)
local DEFAULT_TRANSPARENCY = 0.9 -- default transparency for visible messages
local MAX_MESSAGES = 4 -- Maximum number of messages to display at once

-- Message type colors
local MESSAGE_COLORS = {
    regular = Color3.fromRGB(255, 255, 255), -- White
    info = Color3.fromRGB(100, 150, 255),    -- Blue
    error = Color3.fromRGB(255, 100, 100)    -- Red
}

-- Active messages array (up to 4 messages)
local activeMessages = {}
local messageIdCounter = 0
local isProcessingMessage = false
local messageQueue = {}

-- Create BindableEvents for different message types
local showMessageEvent = ReplicatedStorage:FindFirstChild("ShowMessageEvent")
if not showMessageEvent then
    showMessageEvent = Instance.new("BindableEvent")
    showMessageEvent.Name = "ShowMessageEvent"
    showMessageEvent.Parent = ReplicatedStorage
end

local showInfoMessageEvent = ReplicatedStorage:FindFirstChild("ShowInfoMessageEvent")
if not showInfoMessageEvent then
    showInfoMessageEvent = Instance.new("BindableEvent")
    showInfoMessageEvent.Name = "ShowInfoMessageEvent"
    showInfoMessageEvent.Parent = ReplicatedStorage
end

local showErrorMessageEvent = ReplicatedStorage:FindFirstChild("ShowErrorMessageEvent")
if not showErrorMessageEvent then
    showErrorMessageEvent = Instance.new("BindableEvent")
    showErrorMessageEvent.Name = "ShowErrorMessageEvent"
    showErrorMessageEvent.Parent = ReplicatedStorage
    print("DEBUG: Messages.client.lua - Created ShowErrorMessageEvent")
else
    print("DEBUG: Messages.client.lua - Found existing ShowErrorMessageEvent")
end

-- Function to create a new message label
local function createMessageLabel(text, messageType)
    messageType = messageType or "regular"

    local messageLabel = Instance.new("TextLabel")
    messageLabel.Name = "MessageLabel_" .. messageIdCounter
    messageLabel.Text = text
    messageLabel.Size = UDim2.new(1, 0, 0, 30) -- Full width, 30 pixels height
    messageLabel.BackgroundTransparency = 1 -- No background
    messageLabel.TextColor3 = MESSAGE_COLORS[messageType] or MESSAGE_COLORS.regular
    messageLabel.TextScaled = true -- Enable text scaling
    messageLabel.Font = Enum.Font.Oswald -- Use Oswald font
    messageLabel.TextTransparency = 1 -- Start text invisible

    -- Position at top of frame initially (will tween down)
    messageLabel.Position = UDim2.new(0, 0, 0, -30) -- Start above the frame
    messageLabel.Parent = infoFrame

    messageIdCounter = messageIdCounter + 1
    return messageLabel
end

-- Function to get target position for a message based on its stack index
local function getTargetPosition(stackIndex)
    -- Stack messages from bottom to top (index 1 = bottom, index 4 = top)
    local yOffset = (stackIndex - 1) * 35 -- 35 pixels between messages (30 height + 5 spacing)
    return UDim2.new(0, 0, 1, -yOffset - 30) -- Position from bottom of frame
end

-- Function to update positions of all active messages
local function updateMessagePositions()
    for i, messageData in ipairs(activeMessages) do
        local targetPos = getTargetPosition(i)
        local tween = TweenService:Create(
            messageData.label,
            TweenInfo.new(TWEEN_TIME, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
            {Position = targetPos}
        )
        tween:Play()
    end
end

-- Function to remove a message
local function removeMessage(messageData)
    -- Cancel removal task if it exists and is still running
    if messageData.removalTask then
        pcall(function()
            task.cancel(messageData.removalTask)
        end)
        messageData.removalTask = nil
    end

    -- Find and remove from active messages
    for i, activeMessage in ipairs(activeMessages) do
        if activeMessage == messageData then
            table.remove(activeMessages, i)
            break
        end
    end

    -- Tween out the message (fade text to invisible)
    local fadeOutTween = TweenService:Create(
        messageData.label,
        TweenInfo.new(TWEEN_TIME, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
        {TextTransparency = 1}
    )

    fadeOutTween:Play()
    fadeOutTween.Completed:Connect(function()
        messageData.label:Destroy()

        -- Hide InfoFrame if no messages are left
        if #activeMessages == 0 then
            infoFrame.Visible = false
        end

        -- Update positions of remaining messages after fade out completes
        updateMessagePositions()

        -- Process next message in queue if any
        processMessageQueue()
    end)
end

-- Internal function to actually show a message (without queuing)
local function showMessageInternal(text, messageType)
    isProcessingMessage = true
    messageType = messageType or "regular" -- Default to regular if not specified

    -- Check if this message already exists
    for _, messageData in ipairs(activeMessages) do
        if messageData.originalText == text then
            -- Found duplicate - increment counter and reset timer
            messageData.count = messageData.count + 1
            messageData.startTime = tick()

            -- Update the label text with counter
            messageData.label.Text = text .. " (x" .. messageData.count .. ")"

            -- Cancel existing removal task
            if messageData.removalTask then
                pcall(function()
                    task.cancel(messageData.removalTask)
                end)
            end

            -- Add shake animation for duplicate (quicker animation)
            local originalPos = messageData.label.Position
            local shakeAmount = 5
            local shakeTween1 = TweenService:Create(
                messageData.label,
                TweenInfo.new(0.05, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
                {Position = UDim2.new(originalPos.X.Scale, originalPos.X.Offset + shakeAmount, originalPos.Y.Scale, originalPos.Y.Offset)}
            )
            local shakeTween2 = TweenService:Create(
                messageData.label,
                TweenInfo.new(0.05, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
                {Position = UDim2.new(originalPos.X.Scale, originalPos.X.Offset - shakeAmount, originalPos.Y.Scale, originalPos.Y.Offset)}
            )
            local shakeTween3 = TweenService:Create(
                messageData.label,
                TweenInfo.new(0.05, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
                {Position = originalPos}
            )

            shakeTween1:Play()
            shakeTween1.Completed:Connect(function()
                shakeTween2:Play()
                shakeTween2.Completed:Connect(function()
                    shakeTween3:Play()
                end)
            end)

            -- Schedule new removal task
            messageData.removalTask = task.delay(MESSAGE_DISPLAY_TIME, function()
                removeMessage(messageData)
            end)

            isProcessingMessage = false
            processMessageQueue() -- Process next message
            return messageData
        end
    end

    -- If we're at max messages, remove the oldest one immediately to prevent overlap
    if #activeMessages >= MAX_MESSAGES then
        local oldestMessage = activeMessages[1]

        -- Cancel the oldest message's removal task
        if oldestMessage.removalTask then
            pcall(function()
                task.cancel(oldestMessage.removalTask)
            end)
            oldestMessage.removalTask = nil
        end

        -- Remove from active messages immediately
        table.remove(activeMessages, 1)

        -- Start fading out the oldest message
        local fadeOutTween = TweenService:Create(
            oldestMessage.label,
            TweenInfo.new(TWEEN_TIME, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
            {TextTransparency = 1}
        )
        fadeOutTween:Play()
        fadeOutTween.Completed:Connect(function()
            oldestMessage.label:Destroy()
        end)
    end

    -- Show InfoFrame if we don't have any messages yet (or if we're about to remove one)
    if #activeMessages == 0 or (#activeMessages == MAX_MESSAGES) then
        infoFrame.Visible = true
    end

    -- Create the message label
    local messageLabel = createMessageLabel(text, messageType)

    -- Create message data
    local messageData = {
        label = messageLabel,
        startTime = tick(),
        originalText = text,
        count = 1,
        removalTask = nil
    }

    -- Add to active messages
    table.insert(activeMessages, messageData)

    -- Get target position (messages stack from bottom to top)
    local targetPos = getTargetPosition(#activeMessages)

    -- Start the message above the frame and tween down while fading in
    local startPos = UDim2.new(0, 0, 0, -30) -- Start above frame
    messageLabel.Position = startPos

    -- Create position tween
    local positionTween = TweenService:Create(
        messageLabel,
        TweenInfo.new(TWEEN_TIME, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
        {Position = targetPos}
    )

    -- Use a separate transparency tween that runs alongside the position tween
    local transparencyTween = TweenService:Create(
        messageLabel,
        TweenInfo.new(TWEEN_TIME, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
        {TextTransparency = 0}
    )

    transparencyTween:Play()
    positionTween:Play()

    -- Wait for animation to complete before allowing next message
    positionTween.Completed:Connect(function()
        isProcessingMessage = false
        processMessageQueue() -- Process next message
    end)

    -- Update positions of all messages to maintain proper stacking
    updateMessagePositions()

    -- Schedule message removal after display time
    messageData.removalTask = task.delay(MESSAGE_DISPLAY_TIME, function()
        removeMessage(messageData)
    end)

    return messageData
end

-- Function to process the message queue
local function processMessageQueue()
    if isProcessingMessage or #messageQueue == 0 then
        return
    end

    local queuedMessage = table.remove(messageQueue, 1)
    showMessageInternal(queuedMessage.text, queuedMessage.messageType)
end

-- Function to show a new message (public interface)
local function showMessage(text, messageType)
    -- Add to queue
    table.insert(messageQueue, {text = text, messageType = messageType})

    -- Start processing if not already processing
    processMessageQueue()
end

-- Connect the BindableEvents to show different types of messages
showMessageEvent.Event:Connect(function(text)
    showMessage(text, "regular")
end)

showInfoMessageEvent.Event:Connect(function(text)
    showMessage(text, "info")
end)

showErrorMessageEvent.Event:Connect(function(text)
    print("DEBUG: Messages.client.lua - Received error message:", text)
    showMessage(text, "error")
end)

-- Create additional BindableEvents for other functions
local clearMessagesEvent = ReplicatedStorage:FindFirstChild("ClearMessagesEvent")
if not clearMessagesEvent then
    clearMessagesEvent = Instance.new("BindableEvent")
    clearMessagesEvent.Name = "ClearMessagesEvent"
    clearMessagesEvent.Parent = ReplicatedStorage
end

-- Connect clear messages event
clearMessagesEvent.Event:Connect(function()
    -- Clear all active messages
    for _, messageData in ipairs(activeMessages) do
        -- Cancel removal task
        if messageData.removalTask then
            pcall(function()
                task.cancel(messageData.removalTask)
            end)
        end
        messageData.label:Destroy()
    end
    activeMessages = {}
    infoFrame.Visible = false -- Hide frame when all messages are cleared
end)

--[[
    USAGE INSTRUCTIONS:

    To show different types of messages from any client script:

    Regular Message (White text):
    local ReplicatedStorage = game:GetService("ReplicatedStorage")
    local showMessageEvent = ReplicatedStorage:WaitForChild("ShowMessageEvent")
    showMessageEvent:Fire("Your regular message here!")

    Info Message (Blue text):
    local showInfoMessageEvent = ReplicatedStorage:WaitForChild("ShowInfoMessageEvent")
    showInfoMessageEvent:Fire("Your info message here!")

    Error Message (Red text):
    local showErrorMessageEvent = ReplicatedStorage:WaitForChild("ShowErrorMessageEvent")
    showErrorMessageEvent:Fire("Your error message here!")

    To clear all messages:
    local clearMessagesEvent = ReplicatedStorage:WaitForChild("ClearMessagesEvent")
    clearMessagesEvent:Fire()

    Features:
    - Three message types: regular (white), info (blue), error (red)
    - Up to 4 messages can be displayed simultaneously
    - Messages stack vertically from bottom to top
    - Each message displays for 4 seconds
    - Smooth tween animations for position and transparency
    - Messages tween from above frame down to their stack position while fading in
    - Messages fade out when their time expires
    - Automatic stacking and frame visibility management
    - Oldest messages are removed when limit is reached
--]]

-- Example usage for testing (uncomment to test):
-- task.spawn(function()
--     task.wait(2)
--     showMessageEvent:Fire("Welcome to the game!")
--     showInfoMessageEvent:Fire("This is an info message!")
--     showErrorMessageEvent:Fire("This is an error message!")
--     showMessageEvent:Fire("Messages stack up to 4!")
--     showInfoMessageEvent:Fire("Fifth message replaces oldest!")
-- end)

print("DEBUG: Messages.client.lua - Script loaded successfully, events connected")