-- Example script showing how to use Economy Analytics
-- This script demonstrates various ways to track economy events

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Wait for the EconomyAnalytics module to be available
local EconomyAnalytics
repeat
    EconomyAnalytics = _G.EconomyAnalytics
    if not EconomyAnalytics then
        wait(0.1)
    end
until EconomyAnalytics

print("Economy Analytics Example Script Loaded!")

-- Example: Track when a player buys an object
local function onObjectPurchased(player, objectName, cost)
    -- This will log the purchase as a money sink
    EconomyAnalytics.LogObjectPurchase(player, objectName, cost)
    print("Logged object purchase:", player.Name, "bought", objectName, "for", cost, "money")
end

-- Example: Track when a player sells an object
local function onObjectSold(player, objectName, earnings)
    -- This will log the sale as a money source
    EconomyAnalytics.LogObjectSale(player, objectName, earnings)
    print("Logged object sale:", player.Name, "sold", objectName, "for", earnings, "money")
end

-- Example: Track daily login rewards
local function giveDailyReward(player)
    local rewardAmount = 100
    
    -- Give the money (this would trigger the automatic tracking in Datastore)
    local leaderstats = player:FindFirstChild("leaderstats")
    if leaderstats then
        local money = leaderstats:FindFirstChild("Money")
        if money then
            money.Value = money.Value + rewardAmount
            
            -- Additionally log with specific context for daily rewards
            EconomyAnalytics.LogDailyReward(player, rewardAmount)
            print("Logged daily reward:", player.Name, "received", rewardAmount, "money")
        end
    end
end

-- Example: Track quest completion rewards
local function completeQuest(player, questName, rewardAmount)
    -- Give the money
    local leaderstats = player:FindFirstChild("leaderstats")
    if leaderstats then
        local money = leaderstats:FindFirstChild("Money")
        if money then
            money.Value = money.Value + rewardAmount
            
            -- Log with quest-specific context
            EconomyAnalytics.LogQuestReward(player, rewardAmount, questName)
            print("Logged quest reward:", player.Name, "completed", questName, "for", rewardAmount, "money")
        end
    end
end

-- Example: Track upgrade purchases
local function purchaseUpgrade(player, upgradeName, cost)
    local leaderstats = player:FindFirstChild("leaderstats")
    if leaderstats then
        local money = leaderstats:FindFirstChild("Money")
        if money and money.Value >= cost then
            money.Value = money.Value - cost
            
            -- Log the upgrade purchase
            EconomyAnalytics.LogUpgradePurchase(player, upgradeName, cost)
            print("Logged upgrade purchase:", player.Name, "bought", upgradeName, "for", cost, "money")
            return true
        else
            print("Player doesn't have enough money for upgrade")
            return false
        end
    end
    return false
end

-- Example: Track cosmetic purchases
local function purchaseCosmetic(player, cosmeticName, cost)
    local leaderstats = player:FindFirstChild("leaderstats")
    if leaderstats then
        local money = leaderstats:FindFirstChild("Money")
        if money and money.Value >= cost then
            money.Value = money.Value - cost
            
            -- Log the cosmetic purchase
            EconomyAnalytics.LogCosmeticPurchase(player, cosmeticName, cost)
            print("Logged cosmetic purchase:", player.Name, "bought", cosmeticName, "for", cost, "money")
            return true
        else
            print("Player doesn't have enough money for cosmetic")
            return false
        end
    end
    return false
end

-- Example usage when players join (for demonstration)
Players.PlayerAdded:Connect(function(player)
    -- Wait for player to have leaderstats (after plot selection)
    player:WaitForChild("leaderstats", 30)
    
    -- Example: Give daily reward after 5 seconds
    wait(5)
    if player.Parent then
        giveDailyReward(player)
    end
    
    -- Example: Simulate some purchases after 10 seconds
    wait(5)
    if player.Parent then
        -- These are just examples - in a real game these would be triggered by player actions
        onObjectPurchased(player, "BasicTable", 50)
        wait(2)
        purchaseUpgrade(player, "SpeedBoost", 25)
        wait(2)
        onObjectSold(player, "OldChair", 30)
        wait(2)
        completeQuest(player, "FirstBuild", 75)
    end
end)

-- Export functions for other scripts to use
_G.ExampleEconomyFunctions = {
    onObjectPurchased = onObjectPurchased,
    onObjectSold = onObjectSold,
    giveDailyReward = giveDailyReward,
    completeQuest = completeQuest,
    purchaseUpgrade = purchaseUpgrade,
    purchaseCosmetic = purchaseCosmetic
}

print("Economy Analytics Example Functions Available!")
