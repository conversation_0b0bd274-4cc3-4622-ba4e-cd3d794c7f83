-- ObjectInventory.lua
-- Shared module for object classification and inventory management

local Players = game:GetService("Players")

-- Object classifications with default amounts and image IDs
local OBJECT_CLASSES = {
    Machine = {
        Machine = {
            defaultAmount = 100,  -- Players start with 1 Machine
            imageId = "rbxassetid://0"  -- Replace with actual image ID
        },
        -- Add more machine types here
        -- Drill = {
        --     defaultAmount = 0,
        --     imageId = "rbxassetid://123456789"
        -- },
        -- Conveyor = {
        --     defaultAmount = 0,
        --     imageId = "rbxassetid://987654321"
        -- },
    },
    Structure = {
        ["4x4Wall"] = {
            defaultAmount = 500,  -- Default amount for this structure
            imageId = "rbxassetid://123456789"  -- Replace with actual image ID


        },
        -- Add structure types here
        -- Wall = {
        --     defaultAmount = 0,
        --     imageId = "rbxassetid://111222333"
        -- },
        -- Floor = {
        --     defaultAmount = 0,
        --     imageId = "rbxassetid://444555666"
        -- },
    },
    -- Add more categories as needed
}

local ObjectInventory = {}

-- Flatten the object classes into a single default objects table
local function getDefaultObjects()
    local defaultObjects = {}
    for className, objects in pairs(OBJECT_CLASSES) do
        for objectName, objectData in pairs(objects) do
            defaultObjects[objectName] = objectData.defaultAmount
        end
    end
    return defaultObjects
end

-- Get the class of an object
function ObjectInventory.getObjectClass(objectName)
    for className, objects in pairs(OBJECT_CLASSES) do
        if objects[objectName] then
            return className
        end
    end
    return nil -- Object not found in any class
end

-- Get all objects in a specific class
function ObjectInventory.getObjectsByClass(className)
    return OBJECT_CLASSES[className] or {}
end

-- Get the image ID for an object
function ObjectInventory.getObjectImageId(objectName)
    for className, objects in pairs(OBJECT_CLASSES) do
        if objects[objectName] then
            return objects[objectName].imageId
        end
    end
    return nil -- Object not found or no image ID
end

-- Get all available classes
function ObjectInventory.getAllClasses()
    local classes = {}
    for className, _ in pairs(OBJECT_CLASSES) do
        table.insert(classes, className)
    end
    return classes
end

-- Get default objects (flattened from all classes)
function ObjectInventory.getDefaultObjects()
    return getDefaultObjects()
end

-- Get object count for a player
function ObjectInventory.getObjectCount(player, objectName)
    local objects = player:FindFirstChild("Objects")
    if not objects then return 0 end
    
    local objectValue = objects:FindFirstChild(objectName)
    if not objectValue then return 0 end
    
    return objectValue.Value
end

-- Check if player can place an object (has at least 1)
function ObjectInventory.canPlaceObject(player, objectName)
    local objects = player:FindFirstChild("Objects")
    if not objects then return false end
    
    local objectValue = objects:FindFirstChild(objectName)
    if not objectValue then return false end
    
    return objectValue.Value > 0
end

-- Use an object (decrease count by 1)
function ObjectInventory.useObject(player, objectName)
    local objects = player:FindFirstChild("Objects")
    if not objects then return false end
    
    local objectValue = objects:FindFirstChild(objectName)
    if not objectValue or objectValue.Value <= 0 then return false end
    
    objectValue.Value = objectValue.Value - 1
    return true
end

-- Add objects to inventory
function ObjectInventory.addObject(player, objectName, amount)
    local objects = player:FindFirstChild("Objects")
    if not objects then return false end
    
    local objectValue = objects:FindFirstChild(objectName)
    if not objectValue then
        -- Create new object type if it doesn't exist
        objectValue = Instance.new("IntValue")
        objectValue.Name = objectName
        objectValue.Value = 0
        objectValue.Parent = objects
        
        -- Connect save function for new object (if saveObjects function is available)
        if _G.saveObjects then
            objectValue.Changed:Connect(function()
                local currentObjects = {}
                for _, obj in pairs(objects:GetChildren()) do
                    if obj:IsA("IntValue") then
                        currentObjects[obj.Name] = obj.Value
                    end
                end
                _G.saveObjects(player, currentObjects)
            end)
        end
    end
    
    objectValue.Value = objectValue.Value + amount
    return true
end

-- Get all objects for a player
function ObjectInventory.getAllObjects(player)
    local objects = player:FindFirstChild("Objects")
    if not objects then return {} end
    
    local result = {}
    for _, obj in pairs(objects:GetChildren()) do
        if obj:IsA("IntValue") then
            result[obj.Name] = obj.Value
        end
    end
    return result
end

-- Get objects of a specific class for a player
function ObjectInventory.getPlayerObjectsByClass(player, className)
    local objects = player:FindFirstChild("Objects")
    if not objects then return {} end
    
    local classObjects = OBJECT_CLASSES[className]
    if not classObjects then return {} end
    
    local result = {}
    for objectName, objectData in pairs(classObjects) do
        local objectValue = objects:FindFirstChild(objectName)
        if objectValue then
            result[objectName] = objectValue.Value
        else
            result[objectName] = 0
        end
    end
    return result
end

return ObjectInventory