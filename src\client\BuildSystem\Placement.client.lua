-- Placement.client.lua
-- Handles object placement and adjustment with ghost preview and grid snapping
--
-- FEATURES:
-- - Single-use placement: After placing one object, placement mode stops automatically
-- - Object adjustment: Move existing objects using the same placement system
-- - Collision detection at target position during tweening prevents rapid-click exploits
-- - Grid snapping and rotation support with smooth animations
-- - Plot boundary validation and player collision detection

local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local RunService = game:GetService("RunService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Workspace = game:GetService("Workspace")
local TweenService = game:GetService("TweenService")
local StarterGui = game:GetService("StarterGui")

local player = Players.LocalPlayer
local mouse = player:GetMouse()

-- Message system integration
local showErrorMessageEvent = ReplicatedStorage:WaitForChild("ShowErrorMessageEvent", 5)

print("DEBUG: Placement script - Services loaded successfully")

-- Placement state
local isPlacing = false
local isAdjusting = false -- Whether we're adjusting an existing object
local adjustmentJustStarted = false -- Track when adjustment was just started to prevent immediate touch processing
local currentObject = nil
local isShaking = false -- Track if ghost is currently shaking (prevents placement during shake)
local originalObject = nil -- The original object being adjusted (before creating ghost)
local originalObjectPosition = nil -- Store original position for potential reversion
local originalObjectName = nil -- Store original object name for server communication
local originalObjectParent = nil -- Store original parent to restore object properly
local ghostModel = nil
local placementConnection = nil
local inputConnection = nil
local inputEndedConnection = nil

-- Control UI state
local controlUI = nil
local placeFrame = nil
local mobileFrame = nil
local adjustFrame = nil
local mobileAdjustFrame = nil
local mobileButtonConnections = {}

-- Tool monitoring state
local toolRemovedConnection = nil

-- Rotation state
local currentRotationCFrame = CFrame.new() -- Current rotation as CFrame for relative rotations
local targetRotationCFrame = CFrame.new() -- Target rotation CFrame for tweening
local rotationTween = nil -- Current rotation tween

-- Object rotation memory - remembers last rotation for each object type
local objectRotationMemory = {} -- [objectName] = CFrame

-- Position tweening state
local currentPosition = Vector3.new(0, 0, 0) -- Current ghost position
local targetPosition = Vector3.new(0, 0, 0) -- Target ghost position
local lastValidPosition = nil -- Last valid position for smooth transitions

-- Placement queuing state (for rotation blocking)
local placementQueued = false -- Whether a placement request is queued during rotation

-- Mobile touch state
local mobileFixedPosition = nil -- Fixed position for mobile placement (Vector3)
local lastButtonPressTime = 0 -- Time when last mobile button was pressed
local isDraggingGhost = false -- Whether player is currently dragging the ghost
local dragStartPosition = nil -- Initial touch position when drag started (Vector2)
local dragStartWorldPosition = nil -- Initial world position when drag started (Vector3)
local dragTouchOffset = nil -- Offset from ghost center to initial touch point (Vector3)
local dragConnection = nil -- Connection for drag movement tracking
local activeDragTouchPosition = nil -- Position of the touch that started the drag

-- Side placement tracking (for ghost color validation only)
local isCurrentlySidePlacement = false -- Track if current placement is side placement for ghost color

-- Adjustment timing state
local justStartedAdjustment = false -- Prevent immediate placement after starting adjustment
local mobileAdjustmentConfirmed = false -- Track if mobile adjustment has been confirmed

-- Mobile adjust mode state
local mobileAdjustMode = "Move" -- "Move" or "Remove"
local moveButton = nil
local removeButton = nil

-- Mobile camera control state
local originalCameraType = nil -- Store original camera type
local cameraDisabled = false -- Track if camera is disabled

local cameraUpdateConnection = nil -- Connection for manual camera updates during drag
local lockedCameraOffset = nil -- Store camera offset when drag starts

-- Grid settings
local GRID_SIZE = 1 -- 1 stud grid

-- Rotation settings
local ROTATION_STEP = 90 -- Degrees per rotation step
local ROTATION_TWEEN_TIME = 0.14 -- Time in seconds for rotation animation (reduced from 0.2 for faster rotation)
local ROTATION_TWEEN_STYLE = Enum.EasingStyle.Quart -- Changed from Quad to Quart for snappier feel
local ROTATION_TWEEN_DIRECTION = Enum.EasingDirection.Out

-- Sound settings
local ROTATE_SOUND_ID = "rbxassetid://9119736978" -- Sound for Y-axis rotation
local TURN_SOUND_ID = "rbxassetid://9119740460" -- Sound for Z-axis rotation
local PLACEMENT_SOUND_ID = "rbxassetid://96596470125695" -- Sound for successful placement
local ERROR_SOUND_ID = "rbxassetid://87519554692663" -- Sound for placement failure

-- Position tweening settings
local POSITION_TWEEN_TIME = 0.08 -- Time in seconds for position animation (faster for responsiveness)
local POSITION_TWEEN_STYLE = Enum.EasingStyle.Quad
local POSITION_TWEEN_DIRECTION = Enum.EasingDirection.Out
local POSITION_THRESHOLD = 0.3 -- Minimum distance to trigger tween (reduced for more responsive movement)

-- Forward declarations for functions used in resetPosition
local enableCameraMovement
local stopInputFiltering

-- Forward declarations for functions used in mobile button setup
local placeObject
local rotateObjectY
local rotateObjectZ
local stopPlacement



-- ========================================
-- ADJUST TOOL VARIABLES
-- ========================================
local adjustToolEquipped = false
local adjustHoveredObject = nil
local adjustSelectedObject = nil
local adjustSelectedMode = nil -- Track what mode the object was selected for ("move" or "delete")
local adjustHoverConnection = nil
local adjustInputConnection = nil
local adjustToolActivatedConnection = nil
local lastAdjustedObject = nil -- Track last adjusted object for cooldown
local adjustCooldownTime = 0 -- Cooldown timer to prevent immediate re-selection

-- Adjust tool now uses ghost highlight colors for consistency

-- Mobile adjust tap states for objects
local mobileTapStates = {} -- objectName -> {tapCount, mode, lastTapTime}
local lastButtonPressTime = 0
local BUTTON_PRESS_COOLDOWN = 0.2 -- 200ms cooldown after button press
local TAP_DEBOUNCE_TIME = 0.4 -- 400ms minimum time between taps on same object (increased for remove protection)

-- ========================================
-- MESSAGE AND SOUND HELPER FUNCTIONS
-- ========================================

-- Show error message to player
local function showPlacementError(message)
	if showErrorMessageEvent then
		showErrorMessageEvent:Fire(message)
	else
		warn("ShowErrorMessageEvent not available:", message)
		-- Fallback: Try to find the event again
		local fallbackEvent = ReplicatedStorage:FindFirstChild("ShowErrorMessageEvent")
		if fallbackEvent then
			fallbackEvent:Fire(message)
		else
			warn("ShowErrorMessageEvent still not found on fallback. Message:", message)
			-- Last resort: Use StarterGui notification
			StarterGui:SetCore("ChatMakeSystemMessage", {
				Text = "[ERROR] " .. message;
				Color = Color3.fromRGB(255, 100, 100);
			})
		end
	end
end

-- ========================================
-- SOUND AND ANIMATION HELPER FUNCTIONS
-- ========================================

-- Play sound in the ghost object (or workspace if ghost doesn't exist)
local function playGhostSound(soundId)
	local soundParent = nil

	-- Try to use ghost object first, fallback to workspace
	if ghostModel and ghostModel.PrimaryPart then
		soundParent = ghostModel.PrimaryPart
	else
		-- If no ghost, play sound in workspace for immediate feedback
		soundParent = workspace
	end

	-- Create a temporary sound object
	local sound = Instance.new("Sound")
	sound.SoundId = soundId
	sound.Volume = 0.5
	sound.Parent = soundParent

	-- Play the sound
	sound:Play()

	-- Clean up the sound after it finishes
	sound.Ended:Connect(function()
		sound:Destroy()
	end)

	-- Fallback cleanup in case Ended doesn't fire
	task.spawn(function()
		task.wait(5) -- Wait 5 seconds max
		if sound and sound.Parent then
			sound:Destroy()
		end
	end)
end

-- Play placement sound at a specific position with pitch based on object size
local function playPlacementSound(position, objectModel)
	-- Create a temporary sound object at the placement position
	local sound = Instance.new("Sound")
	sound.SoundId = PLACEMENT_SOUND_ID
	sound.Volume = 2.1

	-- Calculate pitch based on object size (smaller objects = higher pitch)
	local pitch = 1.0 -- Default pitch
	if objectModel then
		-- Get object size using bounding box
		local success, modelCFrame, modelSize = pcall(function()
			return objectModel:GetBoundingBox()
		end)

		if success and modelSize then
			-- Calculate average size (average of X, Y, Z dimensions)
			local averageSize = (modelSize.X + modelSize.Y + modelSize.Z) / 3

			-- Define size thresholds for pitch adjustment
			local baselineSize = 8 -- Objects this size or larger use normal pitch (1.0)
			local maxPitch = 1.5 -- Maximum pitch multiplier for very small objects

			-- Only pitch up for objects smaller than baseline
			if averageSize < baselineSize then
				-- Calculate pitch multiplier (smaller = higher pitch)
				local sizeRatio = averageSize / baselineSize -- 0.25 to 1.0 for small objects
				pitch = 1.0 + (1.0 - sizeRatio) * (maxPitch - 1.0) -- 1.0 to 1.5
				pitch = math.min(pitch, maxPitch) -- Cap at maximum pitch
			end

			print(string.format("Object size: %.1f x %.1f x %.1f (avg: %.1f), Pitch: %.2f",
				modelSize.X, modelSize.Y, modelSize.Z, averageSize, pitch))
		end
	end

	sound.Pitch = pitch
	sound.Parent = workspace

	-- Create a temporary part for positional audio
	local soundPart = Instance.new("Part")
	soundPart.Name = "PlacementSoundPart"
	soundPart.Anchored = true
	soundPart.CanCollide = false
	soundPart.Transparency = 1
	soundPart.Size = Vector3.new(1, 1, 1)
	soundPart.Position = position
	soundPart.Parent = workspace

	-- Parent sound to the part for positional audio
	sound.Parent = soundPart

	-- Play the sound
	sound:Play()

	-- Clean up after sound finishes
	sound.Ended:Connect(function()
		soundPart:Destroy()
	end)

	-- Fallback cleanup
	task.spawn(function()
		task.wait(5) -- Wait 5 seconds max
		if soundPart and soundPart.Parent then
			soundPart:Destroy()
		end
	end)
end

-- Play error sound when placement fails
local function playErrorSound()
	playGhostSound(ERROR_SOUND_ID)
end

-- Simple highlight flash for placement failures
local function shakeGhost()
	if not ghostModel or isShaking then
		return
	end

	isShaking = true

	-- Play error sound immediately
	playErrorSound()

	-- Find the highlight on the ghost
	local highlight = ghostModel:FindFirstChild("Highlight")
	if highlight then
		-- Store original color
		local originalColor = highlight.FillColor

		-- Flash to darker red
		local TweenService = game:GetService("TweenService")
		local darkRedColor = Color3.fromRGB(150, 0, 0) -- Darker red

		-- Quick flash to dark red
		local flashTween = TweenService:Create(
			highlight,
			TweenInfo.new(0.1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
			{FillColor = darkRedColor}
		)

		-- Quick flash back to original
		local returnTween = TweenService:Create(
			highlight,
			TweenInfo.new(0.1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
			{FillColor = originalColor}
		)

		-- Play flash sequence
		flashTween:Play()
		flashTween.Completed:Connect(function()
			returnTween:Play()
			returnTween.Completed:Connect(function()
				isShaking = false
			end)
		end)
	else
		-- No highlight found, just end shake after sound
		task.wait(0.2)
		isShaking = false
	end
end

-- ========================================
-- ADJUSTMENT HELPER FUNCTIONS
-- ========================================

-- Smoothly delete an object with transparency animation
local function smoothDeleteObject(objectToDelete, objectName)
	if not objectToDelete or not objectToDelete:IsA("Model") then
		-- If object doesn't exist or isn't valid, just fire server event
		local deleteEvent = ReplicatedStorage:WaitForChild("Events"):WaitForChild("DeleteObject")
		deleteEvent:FireServer(objectName)
		return
	end

	print("Starting smooth deletion animation for:", objectName)

	-- Collect all parts and GUI elements to tween
	local partsToTween = {}
	local guiElementsToTween = {}

	for _, descendant in pairs(objectToDelete:GetDescendants()) do
		if descendant:IsA("BasePart") then
			table.insert(partsToTween, descendant)
			-- Immediately disable collision so players can walk through during animation
			descendant.CanCollide = false
		elseif descendant:IsA("GuiObject") then
			-- Tween individual GUI elements (TextLabels, ImageLabels, Frames, etc.)
			table.insert(guiElementsToTween, descendant)
		end
	end

	-- Create transparency animation - quick for efficiency
	local tweenInfo = TweenInfo.new(
		0.2, -- Duration: 0.2 seconds for quick deletion
		Enum.EasingStyle.Quad, -- Smooth quad easing
		Enum.EasingDirection.Out, -- Out direction for fading
		0, -- No repeat
		false -- Don't reverse
	)

	local tweensCompleted = 0
	local totalTweens = #partsToTween + #guiElementsToTween

	-- Tween each part to full transparency
	for _, part in pairs(partsToTween) do
		local tween = TweenService:Create(part, tweenInfo, {
			Transparency = 1
		})

		tween.Completed:Connect(function()
			tweensCompleted = tweensCompleted + 1

			-- When all tweens complete, fire the server deletion event
			if tweensCompleted >= totalTweens then
				print("Deletion animation completed, firing server event for:", objectName)
				local deleteEvent = ReplicatedStorage:WaitForChild("Events"):WaitForChild("DeleteObject")
				deleteEvent:FireServer(objectName)
			end
		end)

		tween:Play()
	end

	-- Tween each GUI element to full transparency
	for _, guiElement in pairs(guiElementsToTween) do
		-- Different GUI elements have different transparency properties
		local transparencyProperty = {}

		if guiElement:IsA("TextLabel") or guiElement:IsA("TextButton") or guiElement:IsA("TextBox") then
			transparencyProperty.TextTransparency = 1
			transparencyProperty.BackgroundTransparency = 1
		elseif guiElement:IsA("ImageLabel") or guiElement:IsA("ImageButton") then
			transparencyProperty.ImageTransparency = 1
			transparencyProperty.BackgroundTransparency = 1
		elseif guiElement:IsA("Frame") then
			transparencyProperty.BackgroundTransparency = 1
		else
			-- For other GUI elements, try to set BackgroundTransparency
			transparencyProperty.BackgroundTransparency = 1
		end

		local tween = TweenService:Create(guiElement, tweenInfo, transparencyProperty)

		tween.Completed:Connect(function()
			tweensCompleted = tweensCompleted + 1

			-- When all tweens complete, fire the server deletion event
			if tweensCompleted >= totalTweens then
				print("Deletion animation completed, firing server event for:", objectName)
				local deleteEvent = ReplicatedStorage:WaitForChild("Events"):WaitForChild("DeleteObject")
				deleteEvent:FireServer(objectName)
			end
		end)

		tween:Play()
	end

	-- Fallback: If no parts or GUI elements to tween, just delete immediately
	if totalTweens == 0 then
		print("No parts or GUI elements to animate, deleting immediately:", objectName)
		local deleteEvent = ReplicatedStorage:WaitForChild("Events"):WaitForChild("DeleteObject")
		deleteEvent:FireServer(objectName)
	end
end

-- Create or update highlight for an object
local function createHighlight(object, color, transparency)
	if not object or not object:IsA("Model") then return end

	-- Remove existing highlight
	local existingHighlight = object:FindFirstChild("Highlight")
	if existingHighlight then
		existingHighlight:Destroy()
	end

	-- Create new highlight
	local highlight = Instance.new("Highlight")
	highlight.Name = "Highlight"
	highlight.FillColor = color
	highlight.OutlineColor = color
	highlight.FillTransparency = transparency or 0.6
	highlight.OutlineTransparency = 0
	highlight.Parent = object

	return highlight
end

-- Remove highlight from an object
local function removeHighlight(object)
	if not object then return end

	local highlight = object:FindFirstChild("Highlight")
	if highlight then
		highlight:Destroy()
	end
end

-- Check if an object is a placed object that can be adjusted
local function isAdjustableObject(object)
	if not object or not object:IsA("Model") then
		print("Object is not a model:", object and object.Name or "nil")
		return false
	end

	-- Check if object is in a player's plot
	local plots = Workspace:FindFirstChild("Plots")
	if not plots then
		print("No Plots folder found")
		return false
	end

	print("Checking if object", object.Name, "is adjustable.")
	print("Object parent:", object.Parent and object.Parent.Name or "nil")
	print("Object parent's parent:", object.Parent and object.Parent.Parent and object.Parent.Parent.Name or "nil")

	-- Find which plot this object belongs to
	for _, plotFolder in pairs(plots:GetChildren()) do
		local values = plotFolder:FindFirstChild("Values")
		local ownerId = values and values:FindFirstChild("OwnerId")

		if ownerId and ownerId.Value == player.UserId then
			print("Found player's plot:", plotFolder.Name)
			print("Comparing object.Parent (" .. (object.Parent and object.Parent.Name or "nil") .. ") with plotFolder (" .. plotFolder.Name .. ")")

			-- Check if object is anywhere within this plot's hierarchy
			local function isInPlotHierarchy(obj, plot)
				local current = obj
				while current and current ~= Workspace do
					if current == plot then
						return true
					end
					current = current.Parent
				end
				return false
			end

			if isInPlotHierarchy(object, plotFolder) then
				print("Object is in player's plot hierarchy - adjustable!")
				return true
			end
		end
	end

	print("Object not in player's plot")
	return false
end

-- Get the object under the mouse cursor
local function getObjectUnderMouse()
	local camera = Workspace.CurrentCamera
	local unitRay = camera:ScreenPointToRay(mouse.X, mouse.Y)

	local raycastParams = RaycastParams.new()
	raycastParams.FilterType = Enum.RaycastFilterType.Exclude
	raycastParams.FilterDescendantsInstances = {ghostModel} -- Ignore ghost if it exists

	local raycastResult = Workspace:Raycast(unitRay.Origin, unitRay.Direction * 1000, raycastParams)

	if raycastResult then
		local hitPart = raycastResult.Instance
		local hitModel = hitPart.Parent

		-- Debug: print what we hit (but only occasionally to avoid spam)
		if tick() % 2 < 0.1 then -- Print roughly every 2 seconds
			print("Hit part:", hitPart.Name, "Model:", hitModel and hitModel.Name or "nil")
		end

		-- Check if the hit model is adjustable
		if isAdjustableObject(hitModel) then
			print("Found adjustable object:", hitModel.Name)
			return hitModel
		end
	else
		-- Debug: Print when no raycast hit (occasionally)
		if tick() % 3 < 0.1 then
			print("No raycast hit detected")
		end
	end

	return nil
end

-- Get player's plot folder (same logic as placement script)
local function getPlayerPlotFolder()
	local plots = workspace:FindFirstChild("Plots")
	if not plots then return nil end

	-- Find player's plot by checking OwnerId
	for _, plotFolder in pairs(plots:GetChildren()) do
		local values = plotFolder:FindFirstChild("Values")
		local ownerId = values and values:FindFirstChild("OwnerId")

		if ownerId and ownerId.Value == player.UserId then
			return plotFolder
		end
	end

	return nil
end

-- Get player's plot BasePart (for position/size access)
local function getPlayerPlot()
	local plotFolder = getPlayerPlotFolder()
	if not plotFolder then return nil end

	local plot = plotFolder:FindFirstChild("Plot")
	if plot and plot:IsA("BasePart") then
		return plot
	end

	return nil
end

-- Get object under touch position (for mobile devices)
local function getObjectUnderTouch(touchPosition)
	local camera = workspace.CurrentCamera

	-- Create a ray from the camera through the touch position
	local unitRay = camera:ScreenPointToRay(touchPosition.X, touchPosition.Y)
	local raycastParams = RaycastParams.new()
	raycastParams.FilterType = Enum.RaycastFilterType.Exclude
	raycastParams.FilterDescendantsInstances = {player.Character}

	-- Perform the raycast
	local raycastResult = workspace:Raycast(unitRay.Origin, unitRay.Direction * 1000, raycastParams)

	if raycastResult then
		local hitPart = raycastResult.Instance

		-- Find the model that contains this part
		local model = hitPart.Parent
		while model and not model:IsA("Model") do
			model = model.Parent
		end

		-- Make sure it's not the player's character and belongs to the player
		if model and model ~= player.Character then
			-- Check if this object belongs to the player using hierarchy check
			local playerPlotFolder = getPlayerPlotFolder()
			if playerPlotFolder then
				-- Check if object is anywhere within this plot's hierarchy
				local function isInPlotHierarchy(obj, plot)
					local current = obj
					while current and current ~= workspace do
						if current == plot then
							return true
						end
						current = current.Parent
					end
					return false
				end

				if isInPlotHierarchy(model, playerPlotFolder) then
					-- Additional check: make sure it's actually in the Objects folder
					local objectsFolder = playerPlotFolder:FindFirstChild("Objects")
					if objectsFolder and isInPlotHierarchy(model, objectsFolder) then
						return model
					end
				end
			end
		end
	end

	return nil
end

-- Reset position state
local function resetPosition()
	currentPosition = Vector3.new(0, 0, 0)
	targetPosition = Vector3.new(0, 0, 0)
	lastValidPosition = nil
	mobileFixedPosition = nil
	lastButtonPressTime = 0
	isDraggingGhost = false
	dragStartPosition = nil
	dragStartWorldPosition = nil
	dragTouchOffset = nil
	activeDragTouchPosition = nil
	if dragConnection then
		dragConnection:Disconnect()
		dragConnection = nil
	end
	-- Clean up camera update connection if it exists
	if cameraUpdateConnection then
		cameraUpdateConnection:Disconnect()
		cameraUpdateConnection = nil
	end
	-- Re-enable camera movement if it was disabled
	if enableCameraMovement then
		enableCameraMovement()
	end
	-- Stop input filtering
	if stopInputFiltering then
		stopInputFiltering()
	end
	-- Note: Mobile controls remain enabled
end

-- Reset rotation state
local function resetRotation()
	-- Stop any active tween
	if rotationTween then
		rotationTween:Cancel()
		rotationTween = nil
	end
	
	currentRotationCFrame = CFrame.new()
	targetRotationCFrame = CFrame.new()
	placementQueued = false -- Reset placement queue
end

-- Get current rotation as CFrame (relative rotations)
local function getCurrentRotationCFrame()
	-- Return the current rotation CFrame which accumulates relative rotations
	return currentRotationCFrame
end

-- Check if rotation is currently in progress
local function isRotating()
	return rotationTween ~= nil
end

-- Save the current rotation for the given object
local function saveObjectRotation(objectName)
	if objectName and currentRotationCFrame then
		objectRotationMemory[objectName] = currentRotationCFrame
		print(string.format("Saved rotation for %s: %s", objectName, tostring(currentRotationCFrame)))
	end
end

-- Restore the saved rotation for the given object
local function restoreObjectRotation(objectName)
	if objectName and objectRotationMemory[objectName] then
		local savedRotation = objectRotationMemory[objectName]
		currentRotationCFrame = savedRotation
		targetRotationCFrame = savedRotation
		print(string.format("Restored rotation for %s: %s", objectName, tostring(savedRotation)))
		return true
	end
	return false
end

-- Get the object name from the current object for rotation memory
local function getObjectNameForRotation()
	if currentObject then
		return currentObject.Name
	end
	return nil
end

-- Check if player is on mobile device
local function isMobileDevice()
	local isMobile = UserInputService.TouchEnabled and not UserInputService.KeyboardEnabled
	return isMobile
end

-- Disable camera rotation for mobile during dragging (but keep following player)
local function disableCameraMovement()
	if not isMobileDevice() or cameraDisabled then return end
	
	local camera = Workspace.CurrentCamera
	if camera and player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
		-- Store original camera type and switch to scriptable
		originalCameraType = camera.CameraType
		camera.CameraType = Enum.CameraType.Scriptable
		
		-- Calculate and store the current camera offset from player
		local playerPosition = player.Character.HumanoidRootPart.Position
		local cameraPosition = camera.CFrame.Position
		lockedCameraOffset = cameraPosition - playerPosition
		
		-- Start updating camera to follow player with locked offset
		cameraUpdateConnection = RunService.Heartbeat:Connect(function()
			if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
				local newPlayerPosition = player.Character.HumanoidRootPart.Position
				local newCameraPosition = newPlayerPosition + lockedCameraOffset
				
				-- Keep the same camera orientation (no rotation changes)
				camera.CFrame = CFrame.new(newCameraPosition, newCameraPosition + camera.CFrame.LookVector)
			end
		end)
		
		cameraDisabled = true
		print("Mobile: Camera rotation disabled during drag (camera follows player with locked orientation)")
	end
end

-- Re-enable camera rotation for mobile after dragging
enableCameraMovement = function()
	if not isMobileDevice() or not cameraDisabled then return end
	
	-- Stop manual camera updates
	if cameraUpdateConnection then
		cameraUpdateConnection:Disconnect()
		cameraUpdateConnection = nil
	end
	
	-- Restore original camera type
	local camera = Workspace.CurrentCamera
	if camera and originalCameraType then
		camera.CameraType = originalCameraType
		originalCameraType = nil
	end
	
	-- Clear stored offset
	lockedCameraOffset = nil
	cameraDisabled = false
	print("Mobile: Camera rotation re-enabled after drag")
end

-- Start filtering unwanted inputs during mobile drag
local inputFilterBeganConnection = nil
local inputFilterChangedConnection = nil

local function startInputFiltering()
	if not isMobileDevice() then return end
	
	-- Stop any existing connections
	if inputFilterBeganConnection then
		inputFilterBeganConnection:Disconnect()
		inputFilterBeganConnection = nil
	end
	if inputFilterChangedConnection then
		inputFilterChangedConnection:Disconnect()
		inputFilterChangedConnection = nil
	end
	
	-- Note: Removed input filtering to allow movement while dragging
	print("Mobile: Allowing movement while dragging")
end

-- Stop filtering inputs after mobile drag
stopInputFiltering = function()
	if inputFilterBeganConnection then
		inputFilterBeganConnection:Disconnect()
		inputFilterBeganConnection = nil
	end
	if inputFilterChangedConnection then
		inputFilterChangedConnection:Disconnect()
		inputFilterChangedConnection = nil
	end
	print("Mobile: Stopped input filtering after drag")
end

-- Note: Removed mobile control disabling to allow movement while dragging

-- Check if touch position is on the ghost object (for mobile drag detection)
local function isTouchOnGhostObject(touchPosition)
	if not ghostModel or not ghostModel.Parent then return false end
	
	-- Create raycast to see what the touch hit
	local camera = Workspace.CurrentCamera
	local unitRay = camera:ScreenPointToRay(touchPosition.X, touchPosition.Y)
	
	-- Raycast to see what we hit
	local raycastParams = RaycastParams.new()
	raycastParams.FilterType = Enum.RaycastFilterType.Blacklist
	raycastParams.FilterDescendantsInstances = {} -- Don't filter anything, we want to hit the ghost
	
	local raycastResult = Workspace:Raycast(unitRay.Origin, unitRay.Direction * 1000, raycastParams)
	
	if raycastResult and raycastResult.Instance then
		-- Check if the hit part is part of our ghost model
		local hitPart = raycastResult.Instance
		return hitPart:IsDescendantOf(ghostModel) or hitPart == ghostModel
	end
	
	return false
end

-- Initialize control UI references
local function initializeControlUI()
	if not controlUI then
		controlUI = player.PlayerGui:WaitForChild("ControlUI", 5)
		if controlUI then
			placeFrame = controlUI:FindFirstChild("PlaceFrame")
			mobileFrame = controlUI:FindFirstChild("MobileFrame")
			adjustFrame = controlUI:FindFirstChild("AdjustFrame")
			mobileAdjustFrame = controlUI:FindFirstChild("MobileAdjustFrame")

			-- Initialize mobile adjust buttons
			if mobileAdjustFrame then
				moveButton = mobileAdjustFrame:FindFirstChild("Move")
				removeButton = mobileAdjustFrame:FindFirstChild("Remove")
				print("Placement: Found mobile adjust buttons - Move:", moveButton and moveButton.Name or "nil", "Remove:", removeButton and removeButton.Name or "nil")
			end
		else
			warn("ControlUI not found in PlayerGui")
		end
	end
end

-- Fade PlaceFrame in when in ghost placement mode (PC only)
local function fadeInPlaceFrame()
	if isMobileDevice() then return end -- Only for PC

	initializeControlUI()
	if not placeFrame then return end

	-- Set initial state - frame stays transparent, only content fades
	placeFrame.Visible = true
	placeFrame.BackgroundTransparency = 1 -- Keep frame background invisible

	-- Set all TextLabels to transparent initially
	for _, child in pairs(placeFrame:GetDescendants()) do
		if child:IsA("TextLabel") then
			child.TextTransparency = 1
		end
	end

	-- Create fade in tween for TextLabels only (quicker animation)
	local tweenInfo = TweenInfo.new(0.1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)

	-- Tween only TextLabels to visible
	for _, child in pairs(placeFrame:GetDescendants()) do
		if child:IsA("TextLabel") then
			TweenService:Create(child, tweenInfo, {TextTransparency = 0}):Play()
		end
	end

	print("Fading in PlaceFrame TextLabels")
end

-- Fade PlaceFrame out when exiting ghost placement mode (PC only)
local function fadeOutPlaceFrame()
	if isMobileDevice() then return end -- Only for PC

	if not placeFrame or not placeFrame.Visible then return end

	-- Create fade out tween for TextLabels only (quicker animation)
	local tweenInfo = TweenInfo.new(0.1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)

	local tweensCompleted = 0
	local totalTweens = 0

	-- Count TextLabels to track completion
	for _, child in pairs(placeFrame:GetDescendants()) do
		if child:IsA("TextLabel") then
			totalTweens = totalTweens + 1
		end
	end

	-- If no TextLabels found, just hide the frame
	if totalTweens == 0 then
		placeFrame.Visible = false
		return
	end

	-- Tween TextLabels to transparent
	for _, child in pairs(placeFrame:GetDescendants()) do
		if child:IsA("TextLabel") then
			local tween = TweenService:Create(child, tweenInfo, {TextTransparency = 1})
			tween.Completed:Connect(function()
				tweensCompleted = tweensCompleted + 1
				-- Hide frame after all TextLabels have faded out
				if tweensCompleted >= totalTweens then
					placeFrame.Visible = false
				end
			end)
			tween:Play()
		end
	end

	print("Fading out PlaceFrame TextLabels")
end

-- Show appropriate control UI for placement
local function showControlUI()
	initializeControlUI()
	if not controlUI then return end

	if isMobileDevice() then
		-- Show mobile controls
		if mobileFrame then
			mobileFrame.Visible = true
			print("Showing mobile placement controls")
		end
		-- Fade out PlaceFrame for mobile
		fadeOutPlaceFrame()
	else
		-- Fade in PlaceFrame for PC
		fadeInPlaceFrame()
		if mobileFrame then
			mobileFrame.Visible = false
		end
	end
end

-- Hide control UI
local function hideControlUI()
	-- Fade out PlaceFrame for PC
	fadeOutPlaceFrame()
	if mobileFrame then
		mobileFrame.Visible = false
	end
	print("Hiding placement controls")
end

-- Fade AdjustFrame in when Adjust tool is equipped (PC only)
local function fadeInAdjustFrame()
	if isMobileDevice() then return end -- Only for PC

	initializeControlUI()
	if not adjustFrame then return end

	-- Set initial state - frame stays transparent, only content fades
	adjustFrame.Visible = true
	adjustFrame.BackgroundTransparency = 1 -- Keep frame background invisible

	-- Set all TextLabels to transparent initially
	for _, child in pairs(adjustFrame:GetDescendants()) do
		if child:IsA("TextLabel") then
			child.TextTransparency = 1
		end
	end

	-- Create fade in tween for TextLabels only (quicker animation)
	local tweenInfo = TweenInfo.new(0.1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)

	-- Tween only TextLabels to visible
	for _, child in pairs(adjustFrame:GetDescendants()) do
		if child:IsA("TextLabel") then
			TweenService:Create(child, tweenInfo, {TextTransparency = 0}):Play()
		end
	end

	print("Fading in AdjustFrame TextLabels")
end

-- Fade AdjustFrame out when Adjust tool is unequipped (PC only)
local function fadeOutAdjustFrame()
	if isMobileDevice() then return end -- Only for PC

	if not adjustFrame or not adjustFrame.Visible then return end

	-- Create fade out tween for TextLabels only (quicker animation)
	local tweenInfo = TweenInfo.new(0.1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)

	local tweensCompleted = 0
	local totalTweens = 0

	-- Count TextLabels to track completion
	for _, child in pairs(adjustFrame:GetDescendants()) do
		if child:IsA("TextLabel") then
			totalTweens = totalTweens + 1
		end
	end

	-- If no TextLabels found, just hide the frame
	if totalTweens == 0 then
		adjustFrame.Visible = false
		return
	end

	-- Tween TextLabels to transparent
	for _, child in pairs(adjustFrame:GetDescendants()) do
		if child:IsA("TextLabel") then
			local tween = TweenService:Create(child, tweenInfo, {TextTransparency = 1})
			tween.Completed:Connect(function()
				tweensCompleted = tweensCompleted + 1
				-- Hide frame after all TextLabels have faded out
				if tweensCompleted >= totalTweens then
					adjustFrame.Visible = false
				end
			end)
			tween:Play()
		end
	end

	print("Fading out AdjustFrame TextLabels")
end

-- Store original button size if not already stored
local function storeOriginalButtonSize(button)
	if not button then return end

	-- Store original size if not already stored (use both Scale and Offset)
	if not button:GetAttribute("OriginalSizeX") then
		button:SetAttribute("OriginalSizeX", button.Size.X.Scale)
		button:SetAttribute("OriginalSizeY", button.Size.Y.Scale)
		button:SetAttribute("OriginalOffsetX", button.Size.X.Offset)
		button:SetAttribute("OriginalOffsetY", button.Size.Y.Offset)
	end
end

-- Apply selected styling to a mobile button
local function applySelectedMobileButtonStyle(button)
	if not button then return end

	-- Ensure original size is stored before applying styling
	storeOriginalButtonSize(button)

	-- Create or get UIStroke
	local stroke = button:FindFirstChild("UIStroke")
	if not stroke then
		stroke = Instance.new("UIStroke")
		stroke.Parent = button
	end

	-- Set stroke properties
	stroke.Color = Color3.fromRGB(255, 255, 255) -- White
	stroke.Thickness = 2
	stroke.Enabled = true
	stroke.ApplyStrokeMode = Enum.ApplyStrokeMode.Border

	-- Tween button slightly bigger (only scale height to avoid making it longer)
	local tweenInfo = TweenInfo.new(0.15, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
	local originalSizeX = button:GetAttribute("OriginalSizeX")
	local originalSizeY = button:GetAttribute("OriginalSizeY")
	local originalOffsetX = button:GetAttribute("OriginalOffsetX")
	local originalOffsetY = button:GetAttribute("OriginalOffsetY")

	-- Scale both dimensions evenly by 1.1 for center scaling
	local newSize = UDim2.new(originalSizeX * 1.1, originalOffsetX * 1.1, originalSizeY * 1.1, originalOffsetY * 1.1)

	TweenService:Create(button, tweenInfo, {Size = newSize}):Play()

	-- Tween UIStroke to visible
	TweenService:Create(stroke, tweenInfo, {Transparency = 0}):Play()
end

-- Reset mobile button to unselected styling
local function resetMobileButtonStyle(button)
	if not button then return end

	-- Ensure original size is stored before resetting
	storeOriginalButtonSize(button)

	-- Reset button size to original (including both Scale and Offset)
	local tweenInfo = TweenInfo.new(0.15, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
	local originalSizeX = button:GetAttribute("OriginalSizeX")
	local originalSizeY = button:GetAttribute("OriginalSizeY")
	local originalOffsetX = button:GetAttribute("OriginalOffsetX")
	local originalOffsetY = button:GetAttribute("OriginalOffsetY")
	local originalSize = UDim2.new(originalSizeX, originalOffsetX, originalSizeY, originalOffsetY)

	TweenService:Create(button, tweenInfo, {Size = originalSize}):Play()

	-- Tween UIStroke to invisible
	local stroke = button:FindFirstChild("UIStroke")
	if stroke then
		TweenService:Create(stroke, tweenInfo, {Transparency = 1}):Play()
	end
end

-- Select mobile adjust mode and update button styling
local function selectMobileAdjustMode(mode)
	if not moveButton or not removeButton then return end

	mobileAdjustMode = mode

	-- Update the shared StringValue so other scripts can access the current mode
	local modeValue = ReplicatedStorage:FindFirstChild("MobileAdjustModeValue")
	if modeValue and modeValue:IsA("StringValue") then
		modeValue.Value = mode
		print("Placement: Updated MobileAdjustModeValue to:", mode)
	else
		-- Create the StringValue if it doesn't exist
		modeValue = Instance.new("StringValue")
		modeValue.Name = "MobileAdjustModeValue"
		modeValue.Value = mode
		modeValue.Parent = ReplicatedStorage
		print("Placement: Created MobileAdjustModeValue with mode:", mode)
	end

	-- Reset both buttons to unselected state
	resetMobileButtonStyle(moveButton)
	resetMobileButtonStyle(removeButton)

	-- Apply selected styling to the chosen button
	if mode == "Move" then
		applySelectedMobileButtonStyle(moveButton)
	elseif mode == "Remove" then
		applySelectedMobileButtonStyle(removeButton)
	end

	print("Selected mobile adjust mode:", mode)
end

-- Track if mobile adjust modes have been initialized to prevent multiple initializations
local mobileAdjustModesInitialized = false

-- Initialize mobile adjust mode selection (default to Move on first use, then remember last mode)
local function initializeMobileAdjustModes()
	print("Placement: initializeMobileAdjustModes called, already initialized:", mobileAdjustModesInitialized)

	-- Re-initialize control UI to ensure we have fresh button references
	initializeControlUI()

	print("Placement: After initializeControlUI - moveButton:", moveButton and moveButton.Name or "nil", "removeButton:", removeButton and removeButton.Name or "nil")
	if not moveButton or not removeButton then
		print("Placement: Missing mobile adjust buttons, cannot initialize")
		return
	end

	-- Check if StringValue already exists and has a valid mode
	local modeValue = ReplicatedStorage:FindFirstChild("MobileAdjustModeValue")
	if mobileAdjustModesInitialized and modeValue and modeValue:IsA("StringValue") and (modeValue.Value == "Move" or modeValue.Value == "Remove") then
		-- Already initialized and StringValue has a valid mode - don't override it
		mobileAdjustMode = modeValue.Value
		print("Placement: Already initialized, preserving existing mode from StringValue:", mobileAdjustMode)

		-- Just update the button styling to match the current mode
		selectMobileAdjustMode(mobileAdjustMode)
		return
	end

	-- First time initialization or no valid mode exists
	if modeValue and modeValue:IsA("StringValue") and (modeValue.Value == "Move" or modeValue.Value == "Remove") then
		-- Use the existing mode from StringValue (user has already selected a mode)
		mobileAdjustMode = modeValue.Value
		print("Placement: Using existing mode from StringValue:", mobileAdjustMode)
	else
		-- Set default mode to Move only if no valid mode is set yet
		if not mobileAdjustMode then
			mobileAdjustMode = "Move"
		end
		print("Placement: Setting default mode:", mobileAdjustMode)

		-- Create or update the shared StringValue
		if not modeValue then
			modeValue = Instance.new("StringValue")
			modeValue.Name = "MobileAdjustModeValue"
			modeValue.Parent = ReplicatedStorage
		end
		modeValue.Value = mobileAdjustMode
		print("Placement: Created/Updated MobileAdjustModeValue with mode:", mobileAdjustMode)
	end

	-- Apply styling to the current mode (either existing or default)
	selectMobileAdjustMode(mobileAdjustMode)

	-- Mark as initialized
	mobileAdjustModesInitialized = true

	-- Connect button events
	print("Placement: Connecting button events...")
	moveButton.Activated:Connect(function()
		print("Placement: Move button activated")
		selectMobileAdjustMode("Move")

		-- Notify AdjustTool about button press to prevent immediate object selection
		local buttonPressEvent = ReplicatedStorage:FindFirstChild("MobileButtonPressEvent")
		if not buttonPressEvent then
			buttonPressEvent = Instance.new("BindableEvent")
			buttonPressEvent.Name = "MobileButtonPressEvent"
			buttonPressEvent.Parent = ReplicatedStorage
		end
		buttonPressEvent:Fire()

		-- Notify AdjustTool about mode change to clear tap states
		local modeChangeEvent = ReplicatedStorage:FindFirstChild("MobileModeChangeEvent")
		if not modeChangeEvent then
			modeChangeEvent = Instance.new("BindableEvent")
			modeChangeEvent.Name = "MobileModeChangeEvent"
			modeChangeEvent.Parent = ReplicatedStorage
		end
		modeChangeEvent:Fire("Move")
	end)

	removeButton.Activated:Connect(function()
		print("Placement: Remove button activated")
		selectMobileAdjustMode("Remove")

		-- Notify AdjustTool about button press to prevent immediate object selection
		local buttonPressEvent = ReplicatedStorage:FindFirstChild("MobileButtonPressEvent")
		if not buttonPressEvent then
			buttonPressEvent = Instance.new("BindableEvent")
			buttonPressEvent.Name = "MobileButtonPressEvent"
			buttonPressEvent.Parent = ReplicatedStorage
		end
		buttonPressEvent:Fire()

		-- Notify AdjustTool about mode change to clear tap states
		local modeChangeEvent = ReplicatedStorage:FindFirstChild("MobileModeChangeEvent")
		if not modeChangeEvent then
			modeChangeEvent = Instance.new("BindableEvent")
			modeChangeEvent.Name = "MobileModeChangeEvent"
			modeChangeEvent.Parent = ReplicatedStorage
		end
		modeChangeEvent:Fire("Remove")
	end)

	print("Placement: Button events connected successfully")
	print("Initialized mobile adjust modes - current mode:", mobileAdjustMode)
end

-- Fade MobileAdjustFrame in when Adjust tool is equipped (Mobile only)
local function fadeInMobileAdjustFrame()
	if not isMobileDevice() then return end -- Only for Mobile

	initializeControlUI()
	if not mobileAdjustFrame then return end

	-- Store original sizes for both buttons before any styling operations
	if moveButton then
		storeOriginalButtonSize(moveButton)
	end
	if removeButton then
		storeOriginalButtonSize(removeButton)
	end

	-- Set initial state - frame stays transparent, only buttons fade
	mobileAdjustFrame.Visible = true
	mobileAdjustFrame.BackgroundTransparency = 1 -- Keep frame background invisible

	-- Set all TextButtons to transparent initially (both background and text)
	for _, child in pairs(mobileAdjustFrame:GetDescendants()) do
		if child:IsA("TextButton") then
			child.BackgroundTransparency = 1
			child.TextTransparency = 1

			-- Also set UIStroke to transparent if it exists
			local stroke = child:FindFirstChild("UIStroke")
			if stroke then
				stroke.Transparency = 1
			end
		end
	end

	-- Create fade in tween for TextButtons (quicker animation)
	local tweenInfo = TweenInfo.new(0.1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)

	-- Tween TextButtons to visible (both background and text)
	for _, child in pairs(mobileAdjustFrame:GetDescendants()) do
		if child:IsA("TextButton") then
			TweenService:Create(child, tweenInfo, {BackgroundTransparency = 0, TextTransparency = 0}):Play()
		end
	end

	-- Initialize mode selection (default to Move mode) after a short delay
	task.spawn(function()
		task.wait(0.1) -- Wait for buttons to start fading in
		initializeMobileAdjustModes()
	end)

	print("Fading in MobileAdjustFrame TextButtons")
end

-- Fade MobileAdjustFrame out when Adjust tool is unequipped (Mobile only)
local function fadeOutMobileAdjustFrame()
	if not isMobileDevice() then return end -- Only for Mobile

	if not mobileAdjustFrame or not mobileAdjustFrame.Visible then return end

	-- Reset initialization flag so it can be properly reinitialized next time
	mobileAdjustModesInitialized = false
	print("Placement: Reset mobile adjust modes initialization flag")

	-- Create fade out tween for TextButtons (quicker animation)
	local tweenInfo = TweenInfo.new(0.1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)

	local tweensCompleted = 0
	local totalTweens = 0

	-- Count TextButtons to track completion
	for _, child in pairs(mobileAdjustFrame:GetDescendants()) do
		if child:IsA("TextButton") then
			totalTweens = totalTweens + 1
		end
	end

	-- If no TextButtons found, just hide the frame
	if totalTweens == 0 then
		mobileAdjustFrame.Visible = false
		return
	end

	-- Tween TextButtons to transparent (both background and text)
	for _, child in pairs(mobileAdjustFrame:GetDescendants()) do
		if child:IsA("TextButton") then
			local tween = TweenService:Create(child, tweenInfo, {BackgroundTransparency = 1, TextTransparency = 1})
			tween.Completed:Connect(function()
				tweensCompleted = tweensCompleted + 1
				-- Hide frame after all TextButtons have faded out
				if tweensCompleted >= totalTweens then
					mobileAdjustFrame.Visible = false
				end
			end)
			tween:Play()

			-- Also fade out UIStroke if it exists
			local stroke = child:FindFirstChild("UIStroke")
			if stroke and stroke.Enabled then
				TweenService:Create(stroke, tweenInfo, {Transparency = 1}):Play()
			end
		end
	end

	print("Fading out MobileAdjustFrame TextButtons")
end




-- Listen for AdjustFrame fade events from AdjustTool
local fadeInEvent = ReplicatedStorage:FindFirstChild("FadeInAdjustFrameEvent")
if not fadeInEvent then
	fadeInEvent = Instance.new("BindableEvent")
	fadeInEvent.Name = "FadeInAdjustFrameEvent"
	fadeInEvent.Parent = ReplicatedStorage
end
fadeInEvent.Event:Connect(fadeInAdjustFrame)

local fadeOutEvent = ReplicatedStorage:FindFirstChild("FadeOutAdjustFrameEvent")
if not fadeOutEvent then
	fadeOutEvent = Instance.new("BindableEvent")
	fadeOutEvent.Name = "FadeOutAdjustFrameEvent"
	fadeOutEvent.Parent = ReplicatedStorage
end
fadeOutEvent.Event:Connect(fadeOutAdjustFrame)

-- Listen for MobileAdjustFrame fade events from AdjustTool
local fadeInMobileEvent = ReplicatedStorage:FindFirstChild("FadeInMobileAdjustFrameEvent")
if not fadeInMobileEvent then
	fadeInMobileEvent = Instance.new("BindableEvent")
	fadeInMobileEvent.Name = "FadeInMobileAdjustFrameEvent"
	fadeInMobileEvent.Parent = ReplicatedStorage
end
fadeInMobileEvent.Event:Connect(fadeInMobileAdjustFrame)

local fadeOutMobileEvent = ReplicatedStorage:FindFirstChild("FadeOutMobileAdjustFrameEvent")
if not fadeOutMobileEvent then
	fadeOutMobileEvent = Instance.new("BindableEvent")
	fadeOutMobileEvent.Name = "FadeOutMobileAdjustFrameEvent"
	fadeOutMobileEvent.Parent = ReplicatedStorage
end
fadeOutMobileEvent.Event:Connect(fadeOutMobileAdjustFrame)

-- Create StringValue to share mobile adjust mode with other scripts
local modeValue = ReplicatedStorage:FindFirstChild("MobileAdjustModeValue")
if not modeValue then
	modeValue = Instance.new("StringValue")
	modeValue.Name = "MobileAdjustModeValue"
	modeValue.Value = mobileAdjustMode
	modeValue.Parent = ReplicatedStorage
	print("Placement: Created MobileAdjustModeValue with initial mode:", mobileAdjustMode)
else
	modeValue.Value = mobileAdjustMode
	print("Placement: Updated MobileAdjustModeValue to:", mobileAdjustMode)
end

-- Calculate shortest rotation path between two angles
local function getShortestRotationPath(currentAngle, targetAngle)
	local diff = targetAngle - currentAngle
	
	-- Normalize the difference to be between -180 and 180
	while diff > 180 do
		diff = diff - 360
	end
	while diff < -180 do
		diff = diff + 360
	end
	
	return currentAngle + diff
end

-- Create smooth rotation tween
local function createRotationTween()
	print("Creating rotation tween from", currentRotationCFrame, "to", targetRotationCFrame)
	-- Stop any existing tween (shouldn't happen now since we block during rotation)
	if rotationTween then
		rotationTween:Cancel()
	end
	
	-- Create a CFrameValue to tween between current and target rotation
	local rotationValue = Instance.new("CFrameValue")
	rotationValue.Value = currentRotationCFrame
	
	local tweenInfo = TweenInfo.new(
		ROTATION_TWEEN_TIME,
		ROTATION_TWEEN_STYLE,
		ROTATION_TWEEN_DIRECTION,
		0, -- Repeat count
		false, -- Reverse
		0 -- Delay
	)
	
	-- Create tween for the CFrame rotation
	local tween = TweenService:Create(rotationValue, tweenInfo, {Value = targetRotationCFrame})
	
	-- Update current rotation CFrame during tween
	local connection = rotationValue.Changed:Connect(function()
		currentRotationCFrame = rotationValue.Value
		
		-- Update ghost model rotation if it exists
		if ghostModel and ghostModel.PrimaryPart then
			local position = ghostModel.PrimaryPart.Position
			ghostModel:SetPrimaryPartCFrame(CFrame.new(position) * currentRotationCFrame)
		end
	end)
	
	-- Clean up when tween completes
	tween.Completed:Connect(function()
		currentRotationCFrame = targetRotationCFrame
		connection:Disconnect()
		rotationValue:Destroy()
		rotationTween = nil
		
		-- Handle queued placement
		if placementQueued then
			placementQueued = false
			print("Rotation complete, executing queued placement")
			placeObject()
		end
	end)
	
	-- Store reference to stop if needed
	rotationTween = {
		Cancel = function()
			tween:Cancel()
			connection:Disconnect()
			rotationValue:Destroy()
			rotationTween = nil -- Clear reference when cancelled
		end
	}
	
	tween:Play()
end



-- Setup mobile button connections
local function setupMobileButtons()
	if not mobileFrame then return end
	
	-- Clear existing connections
	for _, connection in pairs(mobileButtonConnections) do
		connection:Disconnect()
	end
	mobileButtonConnections = {}
	
	-- Helper function to connect both mouse and touch events for mobile buttons
	local function connectMobileButton(button, callback, buttonName)
		if button and callback then
			print("Connecting mobile button:", buttonName)
			-- Connect MouseButton1Click for compatibility
			mobileButtonConnections[#mobileButtonConnections + 1] = button.MouseButton1Click:Connect(function()
				print("Mobile " .. buttonName .. " button pressed (MouseButton1Click)")
				lastButtonPressTime = tick()
				callback()
			end)
			
			-- Also connect TouchTap if available for better mobile support
			if button.TouchTap then
				mobileButtonConnections[#mobileButtonConnections + 1] = button.TouchTap:Connect(function()
					print("Mobile " .. buttonName .. " button pressed (TouchTap)")
					lastButtonPressTime = tick()
					callback()
				end)
			end
		else
			print("Mobile button not found:", buttonName)
		end
	end
	
	-- Cancel button
	local cancelBtn = mobileFrame:FindFirstChild("Cancel")
	connectMobileButton(cancelBtn, function()
		if isAdjusting then
			-- During adjustment mode, cancel and restore object to original position
			cancelAdjustment()
		else
			-- During regular placement mode, just stop placement
			stopPlacement()
		end
	end, "cancel")
	
	-- Rotate button (Y-axis rotation)
	local rotateBtn = mobileFrame:FindFirstChild("Rotate")
	connectMobileButton(rotateBtn, function()
		rotateObjectY()
	end, "rotate")
	
	-- Turn button (Z-axis rotation)
	local turnBtn = mobileFrame:FindFirstChild("Turn")
	connectMobileButton(turnBtn, function()
		rotateObjectZ()
	end, "turn")
	
	-- Place button
	local placeBtn = mobileFrame:FindFirstChild("Place")
	connectMobileButton(placeBtn, function()
		placeObject()
	end, "place")
	
	print("Mobile buttons setup complete")
end

-- Cleanup mobile button connections
local function cleanupMobileButtons()
	for _, connection in pairs(mobileButtonConnections) do
		connection:Disconnect()
	end
	mobileButtonConnections = {}
end

-- Setup tool monitoring to cancel placement/adjustment if tool is unequipped
local function setupToolMonitoring()
	local player = Players.LocalPlayer
	if player.Character then
		-- Monitor for tools being removed from character
		toolRemovedConnection = player.Character.ChildRemoved:Connect(function(child)
			if child:IsA("Tool") then
				if child.Name == "Blueprints" and isPlacing then
					print("Blueprints tool was unequipped during placement - cancelling placement")
					-- Don't unequip the tool again since it's already being unequipped
					-- Just clean up the placement state
					isPlacing = false
					currentObject = nil

					-- Clean up rotation tween
					if rotationTween then
						rotationTween:Cancel()
						rotationTween = nil
					end

					-- Reset position state
					resetPosition()

					-- Clean up ghost
					if ghostModel then
						ghostModel:Destroy()
						ghostModel = nil
					end

					-- Disconnect connections
					if placementConnection then
						placementConnection:Disconnect()
						placementConnection = nil
					end

					if inputConnection then
						inputConnection:Disconnect()
						inputConnection = nil
					end

					-- Hide control UI and cleanup mobile buttons
					hideControlUI()
					cleanupMobileButtons()

					-- Clean up tool monitoring
					if toolRemovedConnection then
						toolRemovedConnection:Disconnect()
						toolRemovedConnection = nil
					end
				elseif child.Name == "Adjust" and isAdjusting then
					print("Adjust tool was unequipped during adjustment - cancelling adjustment")
					cancelAdjustment()
				end
			end
		end)


	end
end

-- Cleanup tool monitoring
local function cleanupToolMonitoring()
	if toolRemovedConnection then
		toolRemovedConnection:Disconnect()
		toolRemovedConnection = nil
	end
end





-- ============================================================================
-- COMPLETELY REDESIGNED GRID PLACEMENT SYSTEM
-- Simple, efficient, and ensures ALL objects snap to the same 1x1 grid
-- ============================================================================

-- Use the existing GRID_SIZE from above

-- Note: getPlayerPlot function is defined earlier in the file for adjust tool functionality

-- SIZE-AWARE GRID SNAPPING FUNCTION
-- Properly handles objects with uneven dimensions that need 0.5 offsets
local function snapToGridWithSize(worldPosition, objectSize)
	local playerPlot = getPlayerPlot()

	-- Get object size (with rotation awareness)
	local sizeX = objectSize and objectSize.X or 1
	local sizeY = objectSize and objectSize.Y or 1
	local sizeZ = objectSize and objectSize.Z or 1

	-- Round sizes to nearest integer to handle floating point precision
	sizeX = math.floor(sizeX + 0.5)
	sizeY = math.floor(sizeY + 0.5)
	sizeZ = math.floor(sizeZ + 0.5)

	-- Determine if each dimension is odd (needs 0.5 offset)
	local isOddX = (sizeX % 2) == 1
	local isOddY = (sizeY % 2) == 1
	local isOddZ = (sizeZ % 2) == 1

	if not playerPlot then
		-- No plot found - snap to world grid with size awareness
		local snappedX = isOddX and (math.floor(worldPosition.X / GRID_SIZE) * GRID_SIZE + 0.5) or (math.floor(worldPosition.X / GRID_SIZE + 0.5) * GRID_SIZE)
		local snappedY = isOddY and (math.floor(worldPosition.Y / GRID_SIZE) * GRID_SIZE + 0.5) or (math.floor(worldPosition.Y / GRID_SIZE + 0.5) * GRID_SIZE)
		local snappedZ = isOddZ and (math.floor(worldPosition.Z / GRID_SIZE) * GRID_SIZE + 0.5) or (math.floor(worldPosition.Z / GRID_SIZE + 0.5) * GRID_SIZE)

		return Vector3.new(snappedX, snappedY, snappedZ)
	end

	-- Get plot boundaries
	local plotPos = playerPlot.Position
	local plotSize = playerPlot.Size
	local plotMinX = plotPos.X - plotSize.X/2
	local plotMinZ = plotPos.Z - plotSize.Z/2

	-- Convert world position to plot-relative coordinates
	local relativeX = worldPosition.X - plotMinX
	local relativeZ = worldPosition.Z - plotMinZ

	-- Snap to grid with size awareness
	local snappedX = isOddX and (math.floor(relativeX / GRID_SIZE) * GRID_SIZE + 0.5) or (math.floor(relativeX / GRID_SIZE + 0.5) * GRID_SIZE)
	local snappedZ = isOddZ and (math.floor(relativeZ / GRID_SIZE) * GRID_SIZE + 0.5) or (math.floor(relativeZ / GRID_SIZE + 0.5) * GRID_SIZE)
	local snappedY = isOddY and (math.floor(worldPosition.Y / GRID_SIZE) * GRID_SIZE + 0.5) or (math.floor(worldPosition.Y / GRID_SIZE + 0.5) * GRID_SIZE)

	-- Convert back to world coordinates
	local finalX = plotMinX + snappedX
	local finalZ = plotMinZ + snappedZ

	local result = Vector3.new(finalX, snappedY, finalZ)

	-- Debug output
	print(string.format("SIZE-AWARE GRID: input(%.2f, %.2f, %.2f) size(%.1f, %.1f, %.1f) odd(%s, %s, %s) -> snapped(%.2f, %.2f, %.2f)",
		worldPosition.X, worldPosition.Y, worldPosition.Z,
		sizeX, sizeY, sizeZ,
		tostring(isOddX), tostring(isOddY), tostring(isOddZ),
		result.X, result.Y, result.Z))

	-- Additional debug for rotation issues
	if objectSize then
		print(string.format("  -> Original object size passed: (%.1f, %.1f, %.1f)",
			objectSize.X, objectSize.Y, objectSize.Z))
	end

	return result
end

-- Get rotated object size for grid snapping (mathematical calculation)
local function getRotatedObjectSize(model)
	if not model then
		return Vector3.new(1, 1, 1)
	end

	-- Get the original bounding box
	local _, originalSize = model:GetBoundingBox()

	-- Get the current rotation
	local rotationCFrame = getCurrentRotationCFrame()

	if rotationCFrame == CFrame.new() then
		-- No rotation - use original size
		return originalSize
	end

	-- Calculate rotated dimensions mathematically
	-- This is more reliable than temporarily rotating the model
	local x, y, z = originalSize.X, originalSize.Y, originalSize.Z

	-- Get rotation angles (in radians)
	local _, _, _, r00, r01, r02, r10, r11, r12, r20, r21, r22 = rotationCFrame:GetComponents()

	-- Calculate the rotated bounding box dimensions
	-- This accounts for all possible rotations (90°, 180°, 270° on any axis)
	local rotatedX = math.abs(r00 * x) + math.abs(r01 * y) + math.abs(r02 * z)
	local rotatedY = math.abs(r10 * x) + math.abs(r11 * y) + math.abs(r12 * z)
	local rotatedZ = math.abs(r20 * x) + math.abs(r21 * y) + math.abs(r22 * z)

	local rotatedSize = Vector3.new(rotatedX, rotatedY, rotatedZ)

	-- Debug output for rotation
	print(string.format("ROTATION SIZE: original(%.1f, %.1f, %.1f) -> rotated(%.1f, %.1f, %.1f)",
		originalSize.X, originalSize.Y, originalSize.Z,
		rotatedSize.X, rotatedSize.Y, rotatedSize.Z))

	return rotatedSize
end

-- Get model bottom offset (simplified)
local function getModelBottomOffset(model)
	if not model then return 0 end

	local lowestY = math.huge
	for _, part in pairs(model:GetDescendants()) do
		if part:IsA("BasePart") then
			local partBottom = part.Position.Y - (part.Size.Y / 2)
			lowestY = math.min(lowestY, partBottom)
		end
	end

	if lowestY == math.huge then return 0 end

	-- Calculate offset from model center to bottom
	local centerY = 0
	if model.PrimaryPart then
		centerY = model.PrimaryPart.Position.Y
	else
		local firstPart = model:FindFirstChildOfClass("BasePart")
		if firstPart then
			centerY = firstPart.Position.Y
		end
	end

	return centerY - lowestY
end

-- Function to get the bottom offset of a rotated model
local function getRotatedObjectBottomOffset(model, rotationCFrame)
	if not model or not model.PrimaryPart then
		return 0
	end

	-- Get hitbox size and calculate corners
	local hitboxSize = model.PrimaryPart.Size
	local halfSize = hitboxSize / 2

	-- Calculate all 8 corners of the hitbox with rotation applied
	local corners = {}
	local offsets = {
		Vector3.new(-halfSize.X, -halfSize.Y, -halfSize.Z),
		Vector3.new( halfSize.X, -halfSize.Y, -halfSize.Z),
		Vector3.new(-halfSize.X,  halfSize.Y, -halfSize.Z),
		Vector3.new( halfSize.X,  halfSize.Y, -halfSize.Z),
		Vector3.new(-halfSize.X, -halfSize.Y,  halfSize.Z),
		Vector3.new( halfSize.X, -halfSize.Y,  halfSize.Z),
		Vector3.new(-halfSize.X,  halfSize.Y,  halfSize.Z),
		Vector3.new( halfSize.X,  halfSize.Y,  halfSize.Z)
	}

	-- Apply rotation to each corner and find the lowest Y point
	local lowestY = math.huge
	for _, offset in ipairs(offsets) do
		local rotatedOffset = rotationCFrame:VectorToWorldSpace(offset)
		if rotatedOffset.Y < lowestY then
			lowestY = rotatedOffset.Y
		end
	end

	-- Return the distance from center to lowest point (this is the bottom offset)
	return math.abs(lowestY)
end

-- Calculate proper Y position on plot surface
local function calculatePlotSurfaceY(xzPosition, model)
	local playerPlot = getPlayerPlot()
	if not playerPlot then
		return xzPosition.Y
	end

	local plotSurfaceY = playerPlot.Position.Y + playerPlot.Size.Y/2

	-- Use raycast to find exact surface Y
	local rayOrigin = Vector3.new(xzPosition.X, plotSurfaceY + 10, xzPosition.Z)
	local rayDirection = Vector3.new(0, -20, 0)

	local raycastParams = RaycastParams.new()
	raycastParams.FilterType = Enum.RaycastFilterType.Include
	raycastParams.FilterDescendantsInstances = {playerPlot}

	local raycastResult = Workspace:Raycast(rayOrigin, rayDirection, raycastParams)
	if raycastResult then
		local surfaceY = raycastResult.Position.Y

		-- Add model bottom offset if provided
		if model then
			local bottomOffset = getModelBottomOffset(model)
			return surfaceY + bottomOffset
		end

		return surfaceY
	end

	-- Fallback to plot surface
	return plotSurfaceY
end

-- Helper function to get rotated bounding box size (for collision detection only)
local function getRotatedBoundingSize(model)
	if not model or not model.PrimaryPart then
		return Vector3.new(1, 1, 1) -- Fallback size
	end

	-- Store original CFrame
	local originalCFrame = model.PrimaryPart.CFrame

	-- Apply current rotation to get accurate rotated bounding box
	local currentRotation = getCurrentRotationCFrame()
	local tempCFrame = CFrame.new(originalCFrame.Position) * currentRotation
	model:SetPrimaryPartCFrame(tempCFrame)

	-- Get the rotated bounding box
	local modelCF, modelSize = model:GetBoundingBox()

	-- Restore original CFrame immediately
	model:SetPrimaryPartCFrame(originalCFrame)

	return modelSize
end



-- Create ghost model from original
local function createGhost(originalModel)
	local ghost = originalModel:Clone()
	ghost.Name = originalModel.Name .. "_Ghost"
	
	-- Setup parts for ghost preview
	local function makePartGhost(part)
		if part:IsA("BasePart") then
			-- Make only the hitbox invisible, keep other parts visible for highlight
			if part == ghost.PrimaryPart then
				part.Transparency = 1 -- Make hitbox completely invisible
			end
			-- Don't change transparency of other parts so highlight shows properly
			part.CanCollide = false
			part.Anchored = true
		end
	end
	
	-- Apply to all descendants
	makePartGhost(ghost)
	for _, descendant in pairs(ghost:GetDescendants()) do
		makePartGhost(descendant)
	end
	
	-- Add highlight to the ghost model
	local highlight = Instance.new("Highlight")
	highlight.FillTransparency = 0.6
	highlight.OutlineColor = Color3.fromRGB(106, 171, 255)
	highlight.FillColor = Color3.fromRGB(52, 89, 255) -- Default to good placement color
	highlight.DepthMode = Enum.HighlightDepthMode.Occluded -- Occluded by objects
	highlight.Parent = ghost
	
	ghost.Parent = Workspace
	return ghost
end



-- Set ghost color based on placement validity
local function setGhostColor(ghost, canPlace)
	if not ghost then return end
	
	local highlight = ghost:FindFirstChild("Highlight")
	if highlight then
		if canPlace then
			-- Green for valid placement
			highlight.FillColor = Color3.fromRGB(52, 255, 52)
			highlight.OutlineColor = Color3.fromRGB(106, 255, 106)
		else
			-- Red for invalid placement
			highlight.FillColor = Color3.fromRGB(255, 52, 52)
			highlight.OutlineColor = Color3.fromRGB(255, 106, 106)
		end
	end
end

-- Check if position is on player's plot
local function isPositionOnPlayerPlot(position)
	local plots = Workspace:FindFirstChild("Plots")
	if not plots then return false end
	
	-- Find player's plot
	for _, plotFolder in pairs(plots:GetChildren()) do
		local values = plotFolder:FindFirstChild("Values")
		local ownerId = values and values:FindFirstChild("OwnerId")
		
		if ownerId and ownerId.Value == player.UserId then
			-- Found player's plot, check if position is within it
			local plot = plotFolder:FindFirstChild("Plot")
			if plot and plot:IsA("BasePart") then
				local plotPos = plot.Position
				local plotSize = plot.Size
				
				-- Check if position is within plot bounds
				local minX = plotPos.X - plotSize.X/2
				local maxX = plotPos.X + plotSize.X/2
				local minZ = plotPos.Z - plotSize.Z/2
				local maxZ = plotPos.Z + plotSize.Z/2
				
				if position.X >= minX and position.X <= maxX and 
				   position.Z >= minZ and position.Z <= maxZ then
					return true
				end
			end
			break -- Found player's plot, no need to check others
		end
	end
	
	return false
end

-- Get rotated bounding box corners for a part
local function getRotatedBoundingBox(part)
	local cf = part.CFrame
	local size = part.Size
	local corners = {}
	
	-- Calculate all 8 corners of the bounding box
	local halfSize = size / 2
	local offsets = {
		Vector3.new(-halfSize.X, -halfSize.Y, -halfSize.Z),
		Vector3.new( halfSize.X, -halfSize.Y, -halfSize.Z),
		Vector3.new(-halfSize.X,  halfSize.Y, -halfSize.Z),
		Vector3.new( halfSize.X,  halfSize.Y, -halfSize.Z),
		Vector3.new(-halfSize.X, -halfSize.Y,  halfSize.Z),
		Vector3.new( halfSize.X, -halfSize.Y,  halfSize.Z),
		Vector3.new(-halfSize.X,  halfSize.Y,  halfSize.Z),
		Vector3.new( halfSize.X,  halfSize.Y,  halfSize.Z)
	}
	
	for i, offset in ipairs(offsets) do
		corners[i] = cf:PointToWorldSpace(offset)
	end
	
	return corners
end

-- Get rotated bounding box corners from CFrame and Size
local function getBoundingBoxCorners(cframe, size)
	local corners = {}
	
	-- Calculate all 8 corners of the bounding box
	local halfSize = size / 2
	local offsets = {
		Vector3.new(-halfSize.X, -halfSize.Y, -halfSize.Z),
		Vector3.new( halfSize.X, -halfSize.Y, -halfSize.Z),
		Vector3.new(-halfSize.X,  halfSize.Y, -halfSize.Z),
		Vector3.new( halfSize.X,  halfSize.Y, -halfSize.Z),
		Vector3.new(-halfSize.X, -halfSize.Y,  halfSize.Z),
		Vector3.new( halfSize.X, -halfSize.Y,  halfSize.Z),
		Vector3.new(-halfSize.X,  halfSize.Y,  halfSize.Z),
		Vector3.new( halfSize.X,  halfSize.Y,  halfSize.Z)
	}
	
	for i, offset in ipairs(offsets) do
		corners[i] = cframe:PointToWorldSpace(offset)
	end
	
	return corners
end

-- Get axis-aligned bounding box from rotated corners
local function getAABBFromCorners(corners)
	local minX, maxX = corners[1].X, corners[1].X
	local minY, maxY = corners[1].Y, corners[1].Y
	local minZ, maxZ = corners[1].Z, corners[1].Z
	
	for _, corner in ipairs(corners) do
		minX = math.min(minX, corner.X)
		maxX = math.max(maxX, corner.X)
		minY = math.min(minY, corner.Y)
		maxY = math.max(maxY, corner.Y)
		minZ = math.min(minZ, corner.Z)
		maxZ = math.max(maxZ, corner.Z)
	end
	
	return {
		min = Vector3.new(minX, minY, minZ),
		max = Vector3.new(maxX, maxY, maxZ)
	}
end

-- Check if any players would be inside the object's placement area (client-side)
local function arePlayersInPlacementArea(ghostModel, position)
	if not ghostModel or not ghostModel.PrimaryPart then
		return false -- If no hitbox, allow placement
	end
	
	-- Get the ghost's rotated bounding box
	local hitboxCorners = getRotatedBoundingBox(ghostModel.PrimaryPart)
	local hitboxAABB = getAABBFromCorners(hitboxCorners)
	
	-- Add a buffer to prevent players from getting stuck
	local buffer = 2.0 -- Same buffer as server
	hitboxAABB.min = hitboxAABB.min - Vector3.new(buffer, buffer, buffer)
	hitboxAABB.max = hitboxAABB.max + Vector3.new(buffer, buffer, buffer)
	
	-- Check all players
	for _, otherPlayer in pairs(Players:GetPlayers()) do
		if otherPlayer.Character and otherPlayer.Character:FindFirstChild("HumanoidRootPart") then
			local playerPosition = otherPlayer.Character.HumanoidRootPart.Position
			
			-- Check if player is within the placement area
			if playerPosition.X >= hitboxAABB.min.X and playerPosition.X <= hitboxAABB.max.X and
			   playerPosition.Y >= hitboxAABB.min.Y and playerPosition.Y <= hitboxAABB.max.Y and
			   playerPosition.Z >= hitboxAABB.min.Z and playerPosition.Z <= hitboxAABB.max.Z then
				return true
			end
		end
	end
	
	return false
end

-- Check if any players would be inside the object's placement area at a specific position (client-side)
-- This is used during tweening to validate placement at the target position
local function arePlayersInPlacementAreaAtPosition(ghostModel, targetPosition)
	if not ghostModel or not ghostModel.PrimaryPart then
		return false -- If no hitbox, allow placement
	end
	
	-- Calculate the ghost's rotated bounding box at the target position
	local rotationCFrame = getCurrentRotationCFrame()
	local targetCFrame = CFrame.new(targetPosition) * rotationCFrame
	local hitboxSize = ghostModel.PrimaryPart.Size
	local hitboxCorners = getBoundingBoxCorners(targetCFrame, hitboxSize)
	local hitboxAABB = getAABBFromCorners(hitboxCorners)
	
	-- Add a buffer to prevent players from getting stuck
	local buffer = 2.0 -- Same buffer as server
	hitboxAABB.min = hitboxAABB.min - Vector3.new(buffer, buffer, buffer)
	hitboxAABB.max = hitboxAABB.max + Vector3.new(buffer, buffer, buffer)
	
	-- Check all players
	for _, otherPlayer in pairs(Players:GetPlayers()) do
		if otherPlayer.Character and otherPlayer.Character:FindFirstChild("HumanoidRootPart") then
			local playerPosition = otherPlayer.Character.HumanoidRootPart.Position
			
			-- Check if player is within the placement area
			if playerPosition.X >= hitboxAABB.min.X and playerPosition.X <= hitboxAABB.max.X and
			   playerPosition.Y >= hitboxAABB.min.Y and playerPosition.Y <= hitboxAABB.max.Y and
			   playerPosition.Z >= hitboxAABB.min.Z and playerPosition.Z <= hitboxAABB.max.Z then
				return true
			end
		end
	end
	
	return false
end

-- Check if hitbox (PrimaryPart) would fit within player's plot bounds (rotation-aware)
local function isObjectWithinPlotBounds(objectPosition, model)
	if not model then return isPositionOnPlayerPlot(objectPosition) end
	
	-- Use PrimaryPart (hitbox) for consistent bounds checking with collision system
	if not model.PrimaryPart then
		warn("Model", model.Name, "has no PrimaryPart - cannot check plot bounds accurately")
		return isPositionOnPlayerPlot(objectPosition) -- Fallback to center check
	end
	
	local plots = Workspace:FindFirstChild("Plots")
	if not plots then return false end
	
	-- Find player's plot
	for _, plotFolder in pairs(plots:GetChildren()) do
		local values = plotFolder:FindFirstChild("Values")
		local ownerId = values and values:FindFirstChild("OwnerId")
		
		if ownerId and ownerId.Value == player.UserId then
			local plot = plotFolder:FindFirstChild("Plot")
			if plot and plot:IsA("BasePart") then
				local plotPos = plot.Position
				local plotSize = plot.Size
				
				-- Get plot bounds
				local plotMinX = plotPos.X - plotSize.X/2
				local plotMaxX = plotPos.X + plotSize.X/2
				local plotMinZ = plotPos.Z - plotSize.Z/2
				local plotMaxZ = plotPos.Z + plotSize.Z/2
				
				-- Create a temporary CFrame at the target position with current rotation
				local rotationCFrame = getCurrentRotationCFrame()
				local targetCFrame = CFrame.new(objectPosition) * rotationCFrame
				
				-- Calculate bounding box at target position with rotation
				local hitboxSize = model.PrimaryPart.Size
				local corners = {}
				local halfSize = hitboxSize / 2
				local offsets = {
					Vector3.new(-halfSize.X, -halfSize.Y, -halfSize.Z),
					Vector3.new( halfSize.X, -halfSize.Y, -halfSize.Z),
					Vector3.new(-halfSize.X,  halfSize.Y, -halfSize.Z),
					Vector3.new( halfSize.X,  halfSize.Y, -halfSize.Z),
					Vector3.new(-halfSize.X, -halfSize.Y,  halfSize.Z),
					Vector3.new( halfSize.X, -halfSize.Y,  halfSize.Z),
					Vector3.new(-halfSize.X,  halfSize.Y,  halfSize.Z),
					Vector3.new( halfSize.X,  halfSize.Y,  halfSize.Z)
				}
				
				for i, offset in ipairs(offsets) do
					corners[i] = targetCFrame:PointToWorldSpace(offset)
				end
				
				local objectAABB = getAABBFromCorners(corners)
				
				-- Check if all corners of the rotated object are within plot bounds
				-- Add small tolerance to match constraint function and prevent precision issues
				local tolerance = 0.02
				return objectAABB.min.X >= (plotMinX - tolerance) and objectAABB.max.X <= (plotMaxX + tolerance) and
				       objectAABB.min.Z >= (plotMinZ - tolerance) and objectAABB.max.Z <= (plotMaxZ + tolerance)
			end
			break
		end
	end
	
	return false
end





-- NEW COLLISION SYSTEM: Stop at object sides, allow touching placements
local function checkGhostCollision(ghost)
	if not ghost or not ghost.PrimaryPart then
		return false -- Allow placement if no hitbox defined
	end

	-- Get player's plot folder to check objects within it
	local plots = Workspace:FindFirstChild("Plots")
	if not plots then return false end

	local playerPlotFolder = nil
	for _, plotFolder in pairs(plots:GetChildren()) do
		local values = plotFolder:FindFirstChild("Values")
		local ownerId = values and values:FindFirstChild("OwnerId")

		if ownerId and ownerId.Value == player.UserId then
			playerPlotFolder = plotFolder
			break
		end
	end

	if not playerPlotFolder then return false end

	-- Check objects in the player's plot Objects folder
	local objectsFolder = playerPlotFolder:FindFirstChild("Objects")
	if not objectsFolder then return false end -- No objects placed yet

	-- Get ghost's rotated bounding box
	local ghostCorners = getRotatedBoundingBox(ghost.PrimaryPart)
	local ghostAABB = getAABBFromCorners(ghostCorners)

	-- Check collision with each placed object's hitbox
	for _, placedObject in pairs(objectsFolder:GetChildren()) do
		if placedObject:IsA("Model") and placedObject.PrimaryPart then
			-- Skip collision check with the original object being adjusted
			if isAdjusting and originalObject and placedObject == originalObject then
				continue
			end

			-- Get placed object's rotated bounding box
			local placedCorners = getRotatedBoundingBox(placedObject.PrimaryPart)
			local placedAABB = getAABBFromCorners(placedCorners)

			-- Check for overlap using same logic as server-side collision
			local overlapX = ghostAABB.max.X > placedAABB.min.X and ghostAABB.min.X < placedAABB.max.X
			local overlapY = ghostAABB.max.Y > placedAABB.min.Y and ghostAABB.min.Y < placedAABB.max.Y
			local overlapZ = ghostAABB.max.Z > placedAABB.min.Z and ghostAABB.min.Z < placedAABB.max.Z

			-- If overlapping on all axes, check penetration amounts
			if overlapX and overlapY and overlapZ then
				-- Calculate penetration amounts (same as server logic)
				local penetrationX = math.min(ghostAABB.max.X - placedAABB.min.X, placedAABB.max.X - ghostAABB.min.X)
				local penetrationY = math.min(ghostAABB.max.Y - placedAABB.min.Y, placedAABB.max.Y - ghostAABB.min.Y)
				local penetrationZ = math.min(ghostAABB.max.Z - placedAABB.min.Z, placedAABB.max.Z - ghostAABB.min.Z)

				-- Allow touching placements: if any axis has minimal penetration (≤0.3 studs), allow it
				local touchingTolerance = 0.3 -- Increased from 0.05 to make placement less sensitive
				if penetrationX <= touchingTolerance or penetrationY <= touchingTolerance or penetrationZ <= touchingTolerance then
					continue -- Allow touching/minimal overlap placements
				end

				-- Prevent collision if there's significant penetration on all axes (>0.3 studs)
				-- This prevents objects from being placed inside each other while allowing more forgiving placement
				if penetrationX > touchingTolerance and penetrationY > touchingTolerance and penetrationZ > touchingTolerance then
					return true -- Significant penetration detected - objects would be inside each other
				end
			end
		end
	end

	return false -- No collision
end



-- NEW COLLISION SYSTEM: Stop at object sides, allow touching placements
local function checkGhostCollisionAtPosition(ghost, testPosition)
	if not ghost or not ghost.PrimaryPart then
		return false -- Allow placement if no hitbox defined
	end

	-- Get player's plot folder to check objects within it
	local plots = Workspace:FindFirstChild("Plots")
	if not plots then return false end

	local playerPlotFolder = nil
	for _, plotFolder in pairs(plots:GetChildren()) do
		local values = plotFolder:FindFirstChild("Values")
		local ownerId = values and values:FindFirstChild("OwnerId")

		if ownerId and ownerId.Value == player.UserId then
			playerPlotFolder = plotFolder
			break
		end
	end

	if not playerPlotFolder then return false end

	-- Check objects in the player's plot Objects folder
	local objectsFolder = playerPlotFolder:FindFirstChild("Objects")
	if not objectsFolder then return false end -- No objects placed yet

	-- Calculate ghost's rotated bounding box at the test position
	local currentRotation = getCurrentRotationCFrame()
	local testCFrame = CFrame.new(testPosition) * currentRotation

	-- Get ghost's rotated bounding box corners at test position
	local ghostCorners = getBoundingBoxCorners(testCFrame, ghost.PrimaryPart.Size)
	local ghostAABB = getAABBFromCorners(ghostCorners)

	-- Check collision with each placed object's hitbox
	for _, placedObject in pairs(objectsFolder:GetChildren()) do
		if placedObject:IsA("Model") and placedObject.PrimaryPart then
			-- Skip collision check with the original object being adjusted
			if isAdjusting and originalObject and placedObject == originalObject then
				continue
			end

			-- Get placed object's rotated bounding box
			local placedCorners = getRotatedBoundingBox(placedObject.PrimaryPart)
			local placedAABB = getAABBFromCorners(placedCorners)

			-- Check for overlap using same logic as server-side collision
			local overlapX = ghostAABB.max.X > placedAABB.min.X and ghostAABB.min.X < placedAABB.max.X
			local overlapY = ghostAABB.max.Y > placedAABB.min.Y and ghostAABB.min.Y < placedAABB.max.Y
			local overlapZ = ghostAABB.max.Z > placedAABB.min.Z and ghostAABB.min.Z < placedAABB.max.Z

			-- If overlapping on all axes, check penetration amounts
			if overlapX and overlapY and overlapZ then
				-- Calculate penetration amounts (same as server logic)
				local penetrationX = math.min(ghostAABB.max.X - placedAABB.min.X, placedAABB.max.X - ghostAABB.min.X)
				local penetrationY = math.min(ghostAABB.max.Y - placedAABB.min.Y, placedAABB.max.Y - ghostAABB.min.Y)
				local penetrationZ = math.min(ghostAABB.max.Z - placedAABB.min.Z, placedAABB.max.Z - ghostAABB.min.Z)

				-- Allow touching placements: if any axis has minimal penetration (≤0.3 studs), allow it
				local touchingTolerance = 0.3 -- Increased from 0.05 to make placement less sensitive
				if penetrationX <= touchingTolerance or penetrationY <= touchingTolerance or penetrationZ <= touchingTolerance then
					continue -- Allow touching/minimal overlap placements
				end

				-- Prevent collision if there's significant penetration on all axes (>0.3 studs)
				-- This prevents objects from being placed inside each other while allowing more forgiving placement
				if penetrationX > touchingTolerance and penetrationY > touchingTolerance and penetrationZ > touchingTolerance then
					return true -- Significant penetration detected - objects would be inside each other
				end
			end
		end
	end

	return false -- No collision
end



-- Find the closest valid position by moving ghost to edge of colliding objects
local function findClosestValidPosition(ghost, targetPosition)
	if not ghost or not targetPosition then return targetPosition end

	-- First check if the target position is already valid (no collision)
	if not checkGhostCollisionAtPosition(ghost, targetPosition) then
		return targetPosition -- No collision, position is valid
	end

	-- Get ghost's rotated bounding box at target position
	local currentRotation = getCurrentRotationCFrame()
	local testCFrame = CFrame.new(targetPosition) * currentRotation
	local ghostCorners = getBoundingBoxCorners(testCFrame, ghost.PrimaryPart.Size)
	local ghostAABB = getAABBFromCorners(ghostCorners)

	-- Get player's plot folder to check objects within it
	local plots = Workspace:FindFirstChild("Plots")
	if not plots then return targetPosition end

	local playerPlotFolder = nil
	for _, plotFolder in pairs(plots:GetChildren()) do
		local values = plotFolder:FindFirstChild("Values")
		local ownerId = values and values:FindFirstChild("OwnerId")

		if ownerId and ownerId.Value == player.UserId then
			playerPlotFolder = plotFolder
			break
		end
	end

	if not playerPlotFolder then return targetPosition end

	local objectsFolder = playerPlotFolder:FindFirstChild("Objects")
	if not objectsFolder then return targetPosition end

	-- Find the closest object that's causing collision and move to its edge
	local closestDistance = math.huge
	local bestPosition = targetPosition
	local maxMoveDistance = 8 -- Don't move ghost more than 8 studs away

	for _, placedObject in pairs(objectsFolder:GetChildren()) do
		if placedObject:IsA("Model") and placedObject.PrimaryPart then
			-- Skip collision check with the original object being adjusted
			if isAdjusting and originalObject and placedObject == originalObject then
				continue
			end

			-- Get placed object's bounding box
			local placedCorners = getRotatedBoundingBox(placedObject.PrimaryPart)
			local placedAABB = getAABBFromCorners(placedCorners)

			-- Check if this object is colliding using same tolerance logic
			local overlapX = ghostAABB.max.X > placedAABB.min.X and ghostAABB.min.X < placedAABB.max.X
			local overlapY = ghostAABB.max.Y > placedAABB.min.Y and ghostAABB.min.Y < placedAABB.max.Y
			local overlapZ = ghostAABB.max.Z > placedAABB.min.Z and ghostAABB.min.Z < placedAABB.max.Z

			if overlapX and overlapY and overlapZ then
				-- Calculate penetration amounts to check if this is a significant collision
				local penetrationX = math.min(ghostAABB.max.X - placedAABB.min.X, placedAABB.max.X - ghostAABB.min.X)
				local penetrationY = math.min(ghostAABB.max.Y - placedAABB.min.Y, placedAABB.max.Y - ghostAABB.min.Y)
				local penetrationZ = math.min(ghostAABB.max.Z - placedAABB.min.Z, placedAABB.max.Z - ghostAABB.min.Z)

				-- Allow touching placements: if any axis has minimal penetration (≤0.3 studs), allow it
				local touchingTolerance = 0.3 -- Increased from 0.05 to make placement less sensitive

				-- Skip if this is just touching/minimal overlap
				if penetrationX <= touchingTolerance or penetrationY <= touchingTolerance or penetrationZ <= touchingTolerance then
					continue -- Allow touching/minimal overlap placements
				end

				-- Consider significant penetration as collision - prevent objects from being inside each other
				if penetrationX > touchingTolerance and penetrationY > touchingTolerance and penetrationZ > touchingTolerance then
					-- This object is colliding, find the closest edge position
					local ghostSize = ghostAABB.max - ghostAABB.min
					local ghostHalfSize = ghostSize / 2

					-- Try each face of the placed object
					local edgePositions = {
						-- X faces
						Vector3.new(placedAABB.min.X - ghostHalfSize.X, targetPosition.Y, targetPosition.Z), -- Left side
						Vector3.new(placedAABB.max.X + ghostHalfSize.X, targetPosition.Y, targetPosition.Z), -- Right side
						-- Z faces
						Vector3.new(targetPosition.X, targetPosition.Y, placedAABB.min.Z - ghostHalfSize.Z), -- Front side
						Vector3.new(targetPosition.X, targetPosition.Y, placedAABB.max.Z + ghostHalfSize.Z), -- Back side
					}

					-- Find the closest valid edge position
					for _, edgePos in ipairs(edgePositions) do
						-- Make sure the edge position is on the player's plot
						if isPositionOnPlayerPlot(edgePos) then
							-- Check if this edge position has no collisions
							if not checkGhostCollisionAtPosition(ghost, edgePos) then
								local distance = (edgePos - targetPosition).Magnitude
								-- Only consider positions within 8 studs to prevent jumping too far
								if distance < closestDistance and distance <= maxMoveDistance then
									closestDistance = distance
									bestPosition = edgePos
								end
							end
						end
					end
				end
			end
		end
	end

	return bestPosition
end

-- Set ghost highlight color based on validity
local function setGhostColor(ghost, canPlace)
	if not ghost then return end

	local highlight = ghost:FindFirstChild("Highlight")
	if highlight then
		if canPlace then
			highlight.FillColor = Color3.fromRGB(52, 89, 255) -- Blue for good placement
		else
			highlight.FillColor = Color3.fromRGB(255, 52, 55) -- Red for unplaceable
		end
	end
end

-- Set target position for smooth movement
local function setTargetPosition(newTargetPosition)
	if not ghostModel then return end
	
	-- Calculate distance to see if we should tween
	local distance = (newTargetPosition - currentPosition).Magnitude
	if distance < POSITION_THRESHOLD then
		-- Distance too small, just snap to position
		targetPosition = newTargetPosition
		currentPosition = newTargetPosition
		return
	end
	
	-- Set new target (movement will be handled in updateGhostPosition)
	targetPosition = newTargetPosition
end

-- Update position smoothly (called every frame)
local function updatePositionSmooth()
	if not targetPosition then return end
	
	local distance = (targetPosition - currentPosition).Magnitude
	if distance < 0.01 then
		-- Close enough, snap to target
		currentPosition = targetPosition
		return
	end
	
	-- Smooth interpolation using lerp
	local lerpSpeed = 15 -- Higher = faster, lower = smoother (increased for more responsiveness)
	local alpha = math.min(lerpSpeed * (1/60), 1) -- Assume 60 FPS
	
	-- Apply quadratic ease-out for smooth movement
	alpha = 1 - (1 - alpha) ^ 2
	
	currentPosition = currentPosition:Lerp(targetPosition, alpha)
end



-- Calculate position for side placement with tight hitbox alignment (rotation-aware)
local function calculateSidePlacement(hitPosition, surfaceNormal, model, surfacePart)
	if not model or not surfaceNormal or not surfacePart then
		return hitPosition
	end
	
	-- Use PrimaryPart (hitbox) for consistent sizing with collision system
	if not model.PrimaryPart then
		warn("Model", model.Name, "has no PrimaryPart - cannot calculate side placement accurately")
		return hitPosition
	end
	

	
	-- Get current rotation
	local rotationCFrame = getCurrentRotationCFrame()
	
	-- Use the actual model's bounding box instead of just hitbox size
	-- This accounts for complex rotations and the full object dimensions
	local modelCF, modelSize = model:GetBoundingBox()
	
	-- Create a test CFrame at the hit position with current rotation
	local testCFrame = CFrame.new(hitPosition) * rotationCFrame
	
	-- Calculate the 8 corners of the rotated object's full bounding box
	local halfSize = modelSize * 0.5
	local objectCorners = {}
	local cornerOffsets = {
		Vector3.new(-halfSize.X, -halfSize.Y, -halfSize.Z),
		Vector3.new( halfSize.X, -halfSize.Y, -halfSize.Z),
		Vector3.new(-halfSize.X,  halfSize.Y, -halfSize.Z),
		Vector3.new( halfSize.X,  halfSize.Y, -halfSize.Z),
		Vector3.new(-halfSize.X, -halfSize.Y,  halfSize.Z),
		Vector3.new( halfSize.X, -halfSize.Y,  halfSize.Z),
		Vector3.new(-halfSize.X,  halfSize.Y,  halfSize.Z),
		Vector3.new( halfSize.X,  halfSize.Y,  halfSize.Z)
	}
	
	for i, offset in ipairs(cornerOffsets) do
		objectCorners[i] = testCFrame:PointToWorldSpace(offset)
	end
	
	local objectAABB = getAABBFromCorners(objectCorners)
	
	-- Get surface bounding box
	local surfaceCorners = getRotatedBoundingBox(surfacePart)
	local surfaceAABB = getAABBFromCorners(surfaceCorners)
	
	-- Check surface orientation
	local upVector = Vector3.new(0, 1, 0)
	local isVertical = math.abs(surfaceNormal:Dot(upVector)) < 0.2
	local isUpwardFacing = surfaceNormal:Dot(upVector) > 0.8
	local isDownwardFacing = surfaceNormal:Dot(upVector) < -0.8
	
	local placementPosition = hitPosition
	
	if isVertical then
		-- For vertical surfaces, move the object so its bounding box touches the surface
		if math.abs(surfaceNormal.X) > math.abs(surfaceNormal.Z) then
			-- Placing on X-axis side (left/right of the surface)
			local surfaceEdgeX = surfaceNormal.X > 0 and surfaceAABB.max.X or surfaceAABB.min.X
			local objectEdgeX = surfaceNormal.X > 0 and objectAABB.min.X or objectAABB.max.X
			local offsetX = surfaceEdgeX - objectEdgeX
			placementPosition = Vector3.new(hitPosition.X + offsetX, hitPosition.Y, hitPosition.Z)
			print("Vertical X placement: surfaceEdgeX =", surfaceEdgeX, "objectEdgeX =", objectEdgeX, "offsetX =", offsetX)
		else
			-- Placing on Z-axis side (front/back of the surface)
			local surfaceEdgeZ = surfaceNormal.Z > 0 and surfaceAABB.max.Z or surfaceAABB.min.Z
			local objectEdgeZ = surfaceNormal.Z > 0 and objectAABB.min.Z or objectAABB.max.Z
			local offsetZ = surfaceEdgeZ - objectEdgeZ
			placementPosition = Vector3.new(hitPosition.X, hitPosition.Y, hitPosition.Z + offsetZ)
			print("Vertical Z placement: surfaceEdgeZ =", surfaceEdgeZ, "objectEdgeZ =", objectEdgeZ, "offsetZ =", offsetZ)
		end
	elseif isUpwardFacing then
		-- For top placement, move the object so its bottom touches the surface top
		local surfaceTopY = surfaceAABB.max.Y
		local objectBottomY = objectAABB.min.Y
		local offsetY = surfaceTopY - objectBottomY
		placementPosition = Vector3.new(hitPosition.X, hitPosition.Y + offsetY, hitPosition.Z)
		print("Upward placement: surfaceTopY =", surfaceTopY, "objectBottomY =", objectBottomY, "offsetY =", offsetY)
	elseif isDownwardFacing then
		-- For bottom placement, move the object so its top touches the surface bottom
		local surfaceBottomY = surfaceAABB.min.Y
		local objectTopY = objectAABB.max.Y
		local offsetY = surfaceBottomY - objectTopY
		placementPosition = Vector3.new(hitPosition.X, hitPosition.Y + offsetY, hitPosition.Z)
		print("Downward placement: surfaceBottomY =", surfaceBottomY, "objectTopY =", objectTopY, "offsetY =", offsetY)
	end
	
	return placementPosition
end

-- Prevent objects from sinking below floor level
local function preventFloorSinking(position, model)
	if not model or not model.PrimaryPart then
		return position
	end
	
	-- Get the model's bounding box
	local modelCF, modelSize = model:GetBoundingBox()
	local rotationCFrame = getCurrentRotationCFrame()
	
	-- Create a test CFrame at the position with current rotation
	local testCFrame = CFrame.new(position) * rotationCFrame
	
	-- Calculate the 8 corners of the rotated object's full bounding box
	local halfSize = modelSize * 0.5
	local objectCorners = {}
	local cornerOffsets = {
		Vector3.new(-halfSize.X, -halfSize.Y, -halfSize.Z),
		Vector3.new( halfSize.X, -halfSize.Y, -halfSize.Z),
		Vector3.new(-halfSize.X,  halfSize.Y, -halfSize.Z),
		Vector3.new( halfSize.X,  halfSize.Y, -halfSize.Z),
		Vector3.new(-halfSize.X, -halfSize.Y,  halfSize.Z),
		Vector3.new( halfSize.X, -halfSize.Y,  halfSize.Z),
		Vector3.new(-halfSize.X,  halfSize.Y,  halfSize.Z),
		Vector3.new( halfSize.X,  halfSize.Y,  halfSize.Z)
	}
	
	for i, offset in ipairs(cornerOffsets) do
		objectCorners[i] = testCFrame:PointToWorldSpace(offset)
	end
	
	local objectAABB = getAABBFromCorners(objectCorners)
	
	-- Find the floor level (use the player's plot floor)
	local floorLevel = 0
	local playerPlot = getPlayerPlot()
	if playerPlot then
		floorLevel = playerPlot.Position.Y + playerPlot.Size.Y/2
	end
	
	-- Ensure the bottom of the object doesn't go below the floor level
	local objectBottomY = objectAABB.min.Y
	if objectBottomY < floorLevel then
		local offsetY = floorLevel - objectBottomY
		position = Vector3.new(position.X, position.Y + offsetY, position.Z)
		print("Preventing floor sinking: floorLevel =", floorLevel, "objectBottomY =", objectBottomY, "offsetY =", offsetY)
	end
	
	return position
end

-- Constrain any position to stay within plot boundaries (prevents going outside plot)
local function constrainPositionToPlotBounds(position, model)
	if not model or not model.PrimaryPart then
		return position
	end

	-- Get player's plot
	local playerPlot = getPlayerPlot()
	if not playerPlot then
		return position
	end

	-- Get object size to ensure entire object stays within bounds
	local objectSize = getRotatedObjectSize(model)
	local objectHalfSizeX = objectSize.X / 2
	local objectHalfSizeZ = objectSize.Z / 2

	-- Get plot boundaries
	local plotPos = playerPlot.Position
	local plotSize = playerPlot.Size
	local plotMinX = plotPos.X - plotSize.X/2
	local plotMaxX = plotPos.X + plotSize.X/2
	local plotMinZ = plotPos.Z - plotSize.Z/2
	local plotMaxZ = plotPos.Z + plotSize.Z/2

	-- Constrain object center so that object edges don't exceed plot boundaries
	-- Add small tolerance to prevent precision issues at boundaries
	local tolerance = 0.02
	local constrainedX = math.max(plotMinX + objectHalfSizeX + tolerance, math.min(plotMaxX - objectHalfSizeX - tolerance, position.X))
	local constrainedZ = math.max(plotMinZ + objectHalfSizeZ + tolerance, math.min(plotMaxZ - objectHalfSizeZ - tolerance, position.Z))

	-- Keep original Y position
	local constrainedPosition = Vector3.new(constrainedX, position.Y, constrainedZ)

	-- Debug output if position was constrained
	if constrainedPosition ~= position then
		print("Position constrained to plot bounds:")
		print("  - Original:", position)
		print("  - Constrained:", constrainedPosition)
	end

	return constrainedPosition
end

-- Constrain position to plot bounds while preserving drag direction (for mobile)
local function constrainPositionToPlotBoundsWithDirection(newPosition, previousPosition, model)
	if not model or not model.PrimaryPart then
		return newPosition
	end

	-- Get player's plot
	local playerPlot = getPlayerPlot()
	if not playerPlot then
		return newPosition
	end

	-- Get object size to ensure entire object stays within bounds
	local objectSize = getRotatedObjectSize(model)
	local objectHalfSizeX = objectSize.X / 2
	local objectHalfSizeZ = objectSize.Z / 2

	-- Get plot boundaries
	local plotPos = playerPlot.Position
	local plotSize = playerPlot.Size
	local plotMinX = plotPos.X - plotSize.X/2
	local plotMaxX = plotPos.X + plotSize.X/2
	local plotMinZ = plotPos.Z - plotSize.Z/2
	local plotMaxZ = plotPos.Z + plotSize.Z/2

	-- Add small tolerance to prevent precision issues at boundaries
	local tolerance = 0.02
	local validMinX = plotMinX + objectHalfSizeX + tolerance
	local validMaxX = plotMaxX - objectHalfSizeX - tolerance
	local validMinZ = plotMinZ + objectHalfSizeZ + tolerance
	local validMaxZ = plotMaxZ - objectHalfSizeZ - tolerance

	-- Check if new position is already within bounds
	if newPosition.X >= validMinX and newPosition.X <= validMaxX and
	   newPosition.Z >= validMinZ and newPosition.Z <= validMaxZ then
		return newPosition -- No constraint needed
	end

	-- If we have a previous position, try to preserve the drag direction
	if previousPosition then
		local dragDirection = (newPosition - previousPosition).Unit
		local constrainedPosition = newPosition

		-- Constrain X while trying to preserve direction
		if newPosition.X < validMinX then
			constrainedPosition = Vector3.new(validMinX, newPosition.Y, newPosition.Z)
		elseif newPosition.X > validMaxX then
			constrainedPosition = Vector3.new(validMaxX, newPosition.Y, newPosition.Z)
		end

		-- Constrain Z while trying to preserve direction
		if newPosition.Z < validMinZ then
			constrainedPosition = Vector3.new(constrainedPosition.X, newPosition.Y, validMinZ)
		elseif newPosition.Z > validMaxZ then
			constrainedPosition = Vector3.new(constrainedPosition.X, newPosition.Y, validMaxZ)
		end

		print("Direction-preserving constraint applied:")
		print("  - Original:", newPosition)
		print("  - Constrained:", constrainedPosition)

		return constrainedPosition
	else
		-- No previous position, use simple clamping
		local constrainedX = math.max(validMinX, math.min(validMaxX, newPosition.X))
		local constrainedZ = math.max(validMinZ, math.min(validMaxZ, newPosition.Z))
		return Vector3.new(constrainedX, newPosition.Y, constrainedZ)
	end
end

-- Constrain side placement position to prevent object from going inside plot boundaries
local function constrainSidePlacementToPlotBounds(position, model, hitInstance)
	if not model or not model.PrimaryPart then
		return position
	end
	
	-- Get player's plot
	local playerPlot = getPlayerPlot()
	if not playerPlot then
		return position
	end
	
	-- Check if we're placing against the plot itself
	local isPlacingAgainstPlot = (hitInstance == playerPlot or hitInstance.Parent == playerPlot)
	if not isPlacingAgainstPlot then
		-- If not placing against plot, no need to constrain
		return position
	end
	
	-- Get plot bounds
	local plotPos = playerPlot.Position
	local plotSize = playerPlot.Size
	local plotMinX = plotPos.X - plotSize.X/2
	local plotMaxX = plotPos.X + plotSize.X/2
	local plotMinZ = plotPos.Z - plotSize.Z/2
	local plotMaxZ = plotPos.Z + plotSize.Z/2
	
	-- Calculate object bounding box at the proposed position with current rotation
	local rotationCFrame = getCurrentRotationCFrame()
	local targetCFrame = CFrame.new(position) * rotationCFrame
	
	-- Get model's bounding box
	local modelCF, modelSize = model:GetBoundingBox()
	local halfSize = modelSize * 0.5
	
	-- Calculate the 8 corners of the rotated object
	local cornerOffsets = {
		Vector3.new(-halfSize.X, -halfSize.Y, -halfSize.Z),
		Vector3.new( halfSize.X, -halfSize.Y, -halfSize.Z),
		Vector3.new(-halfSize.X,  halfSize.Y, -halfSize.Z),
		Vector3.new( halfSize.X,  halfSize.Y, -halfSize.Z),
		Vector3.new(-halfSize.X, -halfSize.Y,  halfSize.Z),
		Vector3.new( halfSize.X, -halfSize.Y,  halfSize.Z),
		Vector3.new(-halfSize.X,  halfSize.Y,  halfSize.Z),
		Vector3.new( halfSize.X,  halfSize.Y,  halfSize.Z)
	}
	
	local corners = {}
	for i, offset in ipairs(cornerOffsets) do
		corners[i] = targetCFrame:PointToWorldSpace(offset)
	end
	
	local objectAABB = getAABBFromCorners(corners)
	
	-- Check if object extends into plot and calculate correction
	local correctionX = 0
	local correctionZ = 0

	-- Add tolerance to ensure objects are pushed far enough away to pass validation
	local tolerance = 0.02

	-- Check X-axis penetration
	if objectAABB.min.X < plotMaxX and objectAABB.max.X > plotMinX then
		-- Object overlaps with plot in X direction
		local leftPenetration = plotMaxX - objectAABB.min.X
		local rightPenetration = objectAABB.max.X - plotMinX

		-- Push object away from plot in the direction of least penetration
		if leftPenetration < rightPenetration then
			-- Push object to the left (negative X) with tolerance
			correctionX = -(leftPenetration + tolerance)
		else
			-- Push object to the right (positive X) with tolerance
			correctionX = rightPenetration + tolerance
		end
	end

	-- Check Z-axis penetration
	if objectAABB.min.Z < plotMaxZ and objectAABB.max.Z > plotMinZ then
		-- Object overlaps with plot in Z direction
		local frontPenetration = plotMaxZ - objectAABB.min.Z
		local backPenetration = objectAABB.max.Z - plotMinZ

		-- Push object away from plot in the direction of least penetration
		if frontPenetration < backPenetration then
			-- Push object forward (negative Z) with tolerance
			correctionZ = -(frontPenetration + tolerance)
		else
			-- Push object backward (positive Z) with tolerance
			correctionZ = backPenetration + tolerance
		end
	end
	
	-- Apply corrections
	local correctedPosition = Vector3.new(
		position.X + correctionX,
		position.Y,
		position.Z + correctionZ
	)
	
	if correctionX ~= 0 or correctionZ ~= 0 then
		print("Side placement: Corrected position to prevent plot penetration")
		print("  - Original position:", position)
		print("  - Corrected position:", correctedPosition)
		print("  - Correction X:", correctionX, "Z:", correctionZ)
	end
	
	return correctedPosition
end

-- Get all valid placement surfaces (plot + placed objects)
local function getValidPlacementSurfaces()
	local surfaces = {}
	
	-- Add player's plot
	local playerPlot = getPlayerPlot()
	if playerPlot then
		table.insert(surfaces, playerPlot)
	end
	
	-- Add all placed objects in player's plot
	local plots = Workspace:FindFirstChild("Plots")
	if plots then
		for _, plotFolder in pairs(plots:GetChildren()) do
			local values = plotFolder:FindFirstChild("Values")
			local ownerId = values and values:FindFirstChild("OwnerId")
			
			if ownerId and ownerId.Value == player.UserId then
				local objectsFolder = plotFolder:FindFirstChild("Objects")
				if objectsFolder then
					-- Add all placed objects as valid surfaces
					for _, placedObject in pairs(objectsFolder:GetChildren()) do
						if placedObject:IsA("Model") then
							-- Prioritize PrimaryPart (hitbox) for better side placement detection
							if placedObject.PrimaryPart then
								table.insert(surfaces, placedObject.PrimaryPart)
							end
							
							-- Add all other parts of the placed object as valid surfaces
							for _, part in pairs(placedObject:GetDescendants()) do
								if part:IsA("BasePart") and part ~= placedObject.PrimaryPart then
									table.insert(surfaces, part)
								end
							end
						end
					end
				end
				break
			end
		end
	end
	
	return surfaces
end

-- Calculate initial mobile ghost position in front of player and fully within plot bounds
local function calculateInitialMobilePosition(ghostModel)
	local playerPlot = getPlayerPlot()
	if not playerPlot then
		-- No plot found, return a default position
		return Vector3.new(0, 10, 0)
	end

	-- Get player position and look direction
	local playerPosition = nil
	local playerLookDirection = Vector3.new(0, 0, -1) -- Default forward direction

	if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
		playerPosition = player.Character.HumanoidRootPart.Position
		-- Get player's look direction (forward vector)
		playerLookDirection = player.Character.HumanoidRootPart.CFrame.LookVector
	else
		-- Player character not found, use plot center
		local plotCenter = playerPlot.Position
		local bottomOffset = getModelBottomOffset(ghostModel)
		return Vector3.new(
			plotCenter.X,
			plotCenter.Y + playerPlot.Size.Y/2 + bottomOffset,
			plotCenter.Z
		)
	end

	-- Get object size to ensure it fits within plot bounds and calculate spawn distance
	local objectSize = getRotatedObjectSize(ghostModel)
	local objectHalfSizeX = objectSize.X / 2
	local objectHalfSizeZ = objectSize.Z / 2

	-- Calculate spawn distance based on object size to ensure it's fully in front of player
	-- Use the larger dimension (X or Z) plus some buffer space
	local objectMaxDimension = math.max(objectSize.X, objectSize.Z)
	local SPAWN_DISTANCE = math.max(8, objectMaxDimension + 4) -- At least 8 studs, or object size + 4 studs buffer

	-- Get plot boundaries
	local plotPos = playerPlot.Position
	local plotSize = playerPlot.Size
	local plotMinX = plotPos.X - plotSize.X/2
	local plotMaxX = plotPos.X + plotSize.X/2
	local plotMinZ = plotPos.Z - plotSize.Z/2
	local plotMaxZ = plotPos.Z + plotSize.Z/2
	local plotSurfaceY = plotPos.Y + plotSize.Y/2

	-- Calculate initial position in front of player
	local initialX = playerPosition.X + (playerLookDirection.X * SPAWN_DISTANCE)
	local initialZ = playerPosition.Z + (playerLookDirection.Z * SPAWN_DISTANCE)

	-- Ensure the ENTIRE object fits within plot bounds
	-- Constrain object center so that object edges don't exceed plot boundaries
	local constrainedX = math.max(plotMinX + objectHalfSizeX, math.min(plotMaxX - objectHalfSizeX, initialX))
	local constrainedZ = math.max(plotMinZ + objectHalfSizeZ, math.min(plotMaxZ - objectHalfSizeZ, initialZ))

	-- Calculate proper Y position (on plot surface)
	local bottomOffset = getModelBottomOffset(ghostModel)
	local targetY = plotSurfaceY + bottomOffset

	-- Use size-aware grid snapping
	local tempPosition = Vector3.new(constrainedX, targetY, constrainedZ)
	local snappedPosition = snapToGridWithSize(tempPosition, objectSize)

	-- Final validation: ensure snapped position still keeps object within bounds
	local finalX = math.max(plotMinX + objectHalfSizeX, math.min(plotMaxX - objectHalfSizeX, snappedPosition.X))
	local finalZ = math.max(plotMinZ + objectHalfSizeZ, math.min(plotMaxZ - objectHalfSizeZ, snappedPosition.Z))

	local finalPosition = Vector3.new(finalX, snappedPosition.Y, finalZ)

	-- Debug output
	print(string.format("Mobile: Initial position - Player at (%.1f, %.1f), Look direction (%.2f, %.2f)",
		playerPosition.X, playerPosition.Z, playerLookDirection.X, playerLookDirection.Z))
	print(string.format("Mobile: Object size (%.1f x %.1f), Spawn distance %.1f, Plot bounds X(%.1f to %.1f), Z(%.1f to %.1f)",
		objectSize.X, objectSize.Z, SPAWN_DISTANCE, plotMinX, plotMaxX, plotMinZ, plotMaxZ))
	print(string.format("Mobile: Final ghost position (%.1f, %.1f, %.1f) - fully within plot bounds",
		finalPosition.X, finalPosition.Y, finalPosition.Z))

	return finalPosition
end

-- Update ghost position using raycast to valid placement surfaces (plot + placed objects)
local function updateGhostPosition()
	if not ghostModel then
		if isAdjusting then
			print("DEBUG: updateGhostPosition called but no ghostModel during adjustment!")
		end
		return
	end



	local validSurfaces = getValidPlacementSurfaces()
	if #validSurfaces == 0 then
		-- Hide ghost if no valid surfaces found
		print("DEBUG: No valid surfaces found, hiding ghost")
		ghostModel.Parent = nil
		return
	end
	
	-- Create raycast params that exclude the player character and ghost
	local raycastParams = RaycastParams.new()
	raycastParams.FilterType = Enum.RaycastFilterType.Exclude
	local excludeList = {ghostModel}

	-- Exclude player character to prevent interference when standing on the plot
	if player.Character then
		table.insert(excludeList, player.Character)
	end

	-- During adjustment mode, also exclude the original object if it's still in the plot
	if isAdjusting and originalObject and originalObject.Parent ~= ReplicatedStorage then
		table.insert(excludeList, originalObject)
	end

	raycastParams.FilterDescendantsInstances = excludeList
	
	-- Cast ray from camera through mouse/touch position
	local camera = Workspace.CurrentCamera
	local screenX, screenY
	local finalPosition = nil
	
	if isMobileDevice() then
		-- Mobile: Use fixed position if set, otherwise calculate and set initial position
		if mobileFixedPosition then
			-- Use the fixed position that was set by dragging, but ensure it's constrained
			finalPosition = constrainPositionToPlotBounds(mobileFixedPosition, ghostModel)
			-- Don't reset side placement flag here - mobile dragging might have set it correctly
		else
			-- No fixed position yet, calculate initial position near player and set it as fixed
			-- This is initial mobile positioning, so it's not side placement
			isCurrentlySidePlacement = false
			finalPosition = calculateInitialMobilePosition(ghostModel)
			-- Set this as the fixed position so ghost doesn't follow character movement
			mobileFixedPosition = finalPosition
			print("Mobile: Set initial fixed position for ghost:", finalPosition)
		end
	else
		-- PC: Always follow mouse position
		screenX = mouse.X
		screenY = mouse.Y
		
		local unitRay = camera:ScreenPointToRay(screenX, screenY)
		local raycastResult = Workspace:Raycast(unitRay.Origin, unitRay.Direction * 1000, raycastParams)
		
		if raycastResult then
			-- Validate that we hit a valid placement surface (plot or placed object)
			local hitInstance = raycastResult.Instance
			local isValidSurface = false

			-- Check if hit the player's plot
			local playerPlot = getPlayerPlot()
			if playerPlot and hitInstance == playerPlot then
				isValidSurface = true
			else
				-- Check if hit a placed object in the player's plot
				for _, surface in pairs(validSurfaces) do
					if hitInstance == surface then
						isValidSurface = true
						break
					end
				end
			end

			if isValidSurface then
				local hitNormal = raycastResult.Normal
				local upVector = Vector3.new(0, 1, 0)
				local isUpwardFacing = hitNormal:Dot(upVector) > 0.8
				local isDownwardFacing = hitNormal:Dot(upVector) < -0.8
				local isVertical = math.abs(hitNormal:Dot(upVector)) < 0.2

				if isUpwardFacing or isDownwardFacing or isVertical then
					local hitPosition = raycastResult.Position
					local placementPosition = calculateSidePlacement(hitPosition, hitNormal, ghostModel, raycastResult.Instance)

					if isUpwardFacing then
						-- Not side placement
						isCurrentlySidePlacement = false
						local objectSize = getRotatedObjectSize(ghostModel)
						finalPosition = snapToGridWithSize(placementPosition, objectSize)
					elseif isDownwardFacing then
						-- Not side placement
						isCurrentlySidePlacement = false
						local objectSize = getRotatedObjectSize(ghostModel)
						finalPosition = snapToGridWithSize(placementPosition, objectSize)
					else
						-- For side placement, check if we're placing against the plot or against another object
						local hitInstance = raycastResult.Instance
						local isPlacingAgainstPlot = false

						-- Check if the hit instance is part of the player's plot
						local playerPlot = getPlayerPlot()
						if playerPlot and hitInstance then
							isPlacingAgainstPlot = (hitInstance == playerPlot or hitInstance.Parent == playerPlot)
						end

						-- Update global side placement tracking
						isCurrentlySidePlacement = isPlacingAgainstPlot
						print("Side placement detection: isPlacingAgainstPlot =", isPlacingAgainstPlot, "isCurrentlySidePlacement =", isCurrentlySidePlacement)

						-- Apply plot boundary constraint to prevent object from going inside plot
						local constrainedPosition = constrainSidePlacementToPlotBounds(placementPosition, ghostModel, hitInstance)

						-- Size-aware grid snapping for side placement
						local objectSize = getRotatedObjectSize(ghostModel)
						finalPosition = snapToGridWithSize(constrainedPosition, objectSize)
						print("Side placement - Size-aware grid snap:", finalPosition)
					end
				end
			end
		end
	end
	
	-- If we have a valid final position, update the ghost
	if finalPosition then
		-- Apply floor sinking prevention to ensure objects don't go below floor level
		finalPosition = preventFloorSinking(finalPosition, ghostModel)

		-- Constrain position to plot bounds to prevent going outside
		finalPosition = constrainPositionToPlotBounds(finalPosition, ghostModel)

		-- Apply collision avoidance using same tolerance as plot collision (5cm)
		-- Touching placements are allowed and won't trigger avoidance
		if checkGhostCollisionAtPosition(ghostModel, finalPosition) then
			finalPosition = findClosestValidPosition(ghostModel, finalPosition)
			-- Re-constrain to plot bounds after collision avoidance
			finalPosition = constrainPositionToPlotBounds(finalPosition, ghostModel)
		end

		-- Final check: ensure object is within plot bounds (should always pass now)
		if not isPositionOnPlayerPlot(finalPosition) then
			-- This should rarely happen now, but keep as safety check
			print("Warning: Object position outside plot after constraints:", finalPosition)
			ghostModel.Parent = nil
			return
		end
		
		-- Show ghost on the valid surface
		ghostModel.Parent = Workspace
		
		-- Initialize position if this is the first valid position
		if not lastValidPosition then
			currentPosition = finalPosition
			targetPosition = finalPosition
			lastValidPosition = finalPosition
		end
		
		-- Set target position if it changed significantly
		if (finalPosition - targetPosition).Magnitude > POSITION_THRESHOLD then
			setTargetPosition(finalPosition)
			lastValidPosition = finalPosition
		end
		
		-- Update position smoothly
		updatePositionSmooth()
		
		-- Set the ghost model position with rotation using current (tweened) position
		local rotationCFrame = getCurrentRotationCFrame()
		local finalCFrame = CFrame.new(currentPosition) * rotationCFrame
		
		if ghostModel.PrimaryPart then
			ghostModel:SetPrimaryPartCFrame(finalCFrame)
		else
			-- If no PrimaryPart, we need to move the entire model properly
			-- Calculate the model's current center
			local modelCFrame = ghostModel:GetModelCFrame()
			local offset = currentPosition - modelCFrame.Position
			
			-- Move all parts by the offset and apply rotation
			for _, part in pairs(ghostModel:GetDescendants()) do
				if part:IsA("BasePart") then
					local relativeCFrame = part.CFrame - modelCFrame.Position
					part.CFrame = finalCFrame * relativeCFrame
				end
			end
		end
		
		-- Check for collisions and plot bounds violations at the TARGET position
		-- This prevents placement during tweening when collision detection would be inaccurate
		local hasCollision = checkGhostCollisionAtPosition(ghostModel, finalPosition)
		local playersInArea = arePlayersInPlacementAreaAtPosition(ghostModel, finalPosition)

		-- Simple ghost color validation
		local isWithinPlotBounds = true
		local playerPlot = getPlayerPlot()

		if playerPlot then
			-- Check if this is edge placement (near plot boundary)
			local plotPos = playerPlot.Position
			local plotSize = playerPlot.Size
			local plotMinX = plotPos.X - plotSize.X/2 - 20 -- 20 stud tolerance for edge
			local plotMaxX = plotPos.X + plotSize.X/2 + 20
			local plotMinZ = plotPos.Z - plotSize.Z/2 - 20
			local plotMaxZ = plotPos.Z + plotSize.Z/2 + 20

			-- Allow if within expanded plot area (includes edge placement)
			isWithinPlotBounds = (finalPosition.X >= plotMinX and finalPosition.X <= plotMaxX and
			                     finalPosition.Z >= plotMinZ and finalPosition.Z <= plotMaxZ)
		else
			isWithinPlotBounds = false
		end

		local canPlace = not hasCollision and isWithinPlotBounds and not playersInArea

		setGhostColor(ghostModel, canPlace)
	else
		-- No valid position found, hide ghost
		ghostModel.Parent = nil
	end
end

-- Helper function to find a valid position for rotated ghost (for mobile)
local function findValidPositionAfterRotation(ghostModel, originalPosition, newRotationCFrame)
	if not ghostModel or not ghostModel.PrimaryPart then
		return originalPosition
	end
	
	-- Get valid placement surfaces
	local validSurfaces = getValidPlacementSurfaces()
	if #validSurfaces == 0 then
		return originalPosition
	end
	
	-- Create raycast params
	local raycastParams = RaycastParams.new()
	raycastParams.FilterType = Enum.RaycastFilterType.Include
	raycastParams.FilterDescendantsInstances = validSurfaces
	
	-- Function to calculate proper Y position for rotated object at given X,Z
	local function calculateProperYPosition(xPos, zPos)
		-- Cast ray downward from above to find the surface
		local rayOrigin = Vector3.new(xPos, originalPosition.Y + 50, zPos)
		local rayDirection = Vector3.new(0, -100, 0)
		
		local raycastResult = Workspace:Raycast(rayOrigin, rayDirection, raycastParams)
		if raycastResult then
			local surfaceY = raycastResult.Position.Y
			
			-- Calculate how much offset we need based on the rotated object's lowest point
			local bottomOffset = getRotatedObjectBottomOffset(ghostModel, newRotationCFrame)
			
			-- Position the object so its lowest point sits on the surface
			local properY = surfaceY + bottomOffset
			
			print("Surface Y:", surfaceY, "Bottom offset:", bottomOffset, "Final Y:", properY)
			return Vector3.new(xPos, properY, zPos)
		end
		
		-- Fallback to original Y if no surface found
		return Vector3.new(xPos, originalPosition.Y, zPos)
	end
	
	-- Try the original X,Z position first with proper Y calculation
	local testPosition = calculateProperYPosition(originalPosition.X, originalPosition.Z)
	
	-- Apply rotation and test
	ghostModel:SetPrimaryPartCFrame(CFrame.new(testPosition) * newRotationCFrame)
	
	local hasCollision = checkGhostCollisionAtPosition(ghostModel, testPosition)
	local isWithinPlotBounds = isObjectWithinPlotBounds(testPosition, ghostModel)
	local playersInArea = arePlayersInPlacementAreaAtPosition(ghostModel, testPosition)
	
	if not hasCollision and isWithinPlotBounds and not playersInArea then
		-- Original X,Z position is valid with proper Y
		print("Original X,Z position valid with adjusted Y:", testPosition.Y)
		return testPosition
	end
	
	-- Original position is invalid, try to find a nearby valid position
	local searchRadius = 2 -- Search within 2 studs
	local searchStep = 0.5 -- Check every 0.5 studs
	
	-- Try positions in a spiral pattern around the original position
	for radius = searchStep, searchRadius, searchStep do
		for angle = 0, 360, 45 do -- Check 8 directions
			local radians = math.rad(angle)
			local offsetX = math.cos(radians) * radius
			local offsetZ = math.sin(radians) * radius
			
			local newX = originalPosition.X + offsetX
			local newZ = originalPosition.Z + offsetZ
			
			-- Use size-aware grid snapping
			local objectSize = getRotatedObjectSize(ghostModel)
			local tempPos = Vector3.new(newX, 0, newZ)
			local snappedPos = snapToGridWithSize(tempPos, objectSize)
			newX = snappedPos.X
			newZ = snappedPos.Z
			
			-- Calculate proper Y position for this X,Z location
			testPosition = calculateProperYPosition(newX, newZ)
			
			-- Test this position with rotation
			ghostModel:SetPrimaryPartCFrame(CFrame.new(testPosition) * newRotationCFrame)
			
			local testHasCollision = checkGhostCollisionAtPosition(ghostModel, testPosition)
			local testIsWithinPlotBounds = isObjectWithinPlotBounds(testPosition, ghostModel)
			local testPlayersInArea = arePlayersInPlacementAreaAtPosition(ghostModel, testPosition)
			
			if not testHasCollision and testIsWithinPlotBounds and not testPlayersInArea then
				print("Found valid position after rotation at offset:", offsetX, offsetZ, "Y:", testPosition.Y)
				return testPosition
			end
		end
	end
	
	-- No valid position found, return original position with proper Y calculation
	local fallbackPosition = calculateProperYPosition(originalPosition.X, originalPosition.Z)
	print("No valid position found after rotation, using fallback with proper Y:", fallbackPosition.Y)
	return fallbackPosition
end

-- Rotate object on Y-axis (R key) with collision detection
rotateObjectY = function()
	-- Play rotation sound immediately before any checks
	playGhostSound(ROTATE_SOUND_ID)

	if not (isPlacing or isAdjusting) or not ghostModel then
		print("Cannot rotate Y: isPlacing =", isPlacing, "isAdjusting =", isAdjusting, "ghostModel =", ghostModel)
		return
	end

	-- Block rotation if another rotation is in progress
	if isRotating() then
		print("Cannot rotate Y: waiting for current rotation to finish")
		return
	end

	-- Calculate new rotation by applying a Y-axis rotation to the current rotation
	local yRotationStep = CFrame.fromAxisAngle(Vector3.new(0, 1, 0), math.rad(ROTATION_STEP))
	local newRotationCFrame = currentRotationCFrame * yRotationStep
	
	-- Store original rotation for potential revert
	local originalRotationCFrame = targetRotationCFrame
	targetRotationCFrame = newRotationCFrame
	
	-- Update ghost rotation to test collision
	if ghostModel and ghostModel.PrimaryPart then
		local currentPosition = ghostModel.PrimaryPart.Position

		-- Recalculate Y position to ensure hitbox touches surface after rotation
		local adjustedPosition = currentPosition

		-- Find the surface below the current X,Z position
		local validSurfaces = getValidPlacementSurfaces()
		if #validSurfaces > 0 then
			local raycastParams = RaycastParams.new()
			raycastParams.FilterType = Enum.RaycastFilterType.Include
			raycastParams.FilterDescendantsInstances = validSurfaces

			-- Cast ray from above current position downward
			local rayOrigin = Vector3.new(currentPosition.X, currentPosition.Y + 50, currentPosition.Z)
			local rayDirection = Vector3.new(0, -100, 0)
			local raycastResult = Workspace:Raycast(rayOrigin, rayDirection, raycastParams)

			if raycastResult then
				-- Calculate proper Y position based on rotated hitbox
				local surfaceY = raycastResult.Position.Y
				local rotatedBottomOffset = getRotatedObjectBottomOffset(ghostModel, targetRotationCFrame)
				adjustedPosition = Vector3.new(currentPosition.X, surfaceY + rotatedBottomOffset, currentPosition.Z)
			end
		end

		-- Apply rotation at the properly adjusted position
		ghostModel:SetPrimaryPartCFrame(CFrame.new(adjustedPosition) * targetRotationCFrame)

		-- Update mobile fixed position if on mobile
		if isMobileDevice() and mobileFixedPosition then
			-- Apply collision avoidance only for significant collisions (>0.2 studs on all axes)
			if checkGhostCollisionAtPosition(ghostModel, adjustedPosition) then
				adjustedPosition = findClosestValidPosition(ghostModel, adjustedPosition)
			end
			mobileFixedPosition = adjustedPosition
		end

		-- Always proceed with rotation tween
		print("Rotating Y (relative rotation)")
		createRotationTween()

		-- Update ghost color based on placement validity after rotation
		local hasCollision = checkGhostCollisionAtPosition(ghostModel, adjustedPosition)
		local playersInArea = arePlayersInPlacementAreaAtPosition(ghostModel, adjustedPosition)

		-- For rotation, use simplified bounds checking that allows edge placement
		local isWithinPlotBounds = true
		local playerPlot = getPlayerPlot()
		if playerPlot then
			local plotPos = playerPlot.Position
			local plotSize = playerPlot.Size
			local tolerance = 25 -- Allow edge placement
			local minX = plotPos.X - plotSize.X/2 - tolerance
			local maxX = plotPos.X + plotSize.X/2 + tolerance
			local minZ = plotPos.Z - plotSize.Z/2 - tolerance
			local maxZ = plotPos.Z + plotSize.Z/2 + tolerance

			isWithinPlotBounds = (adjustedPosition.X >= minX and adjustedPosition.X <= maxX and
			                     adjustedPosition.Z >= minZ and adjustedPosition.Z <= maxZ)
		end

		local canPlace = not hasCollision and isWithinPlotBounds and not playersInArea
		setGhostColor(ghostModel, canPlace)
	else
		-- No collision checking possible, allow rotation
		print("Rotating Y (relative rotation, no collision check)")
		createRotationTween()
	end
end

-- Rotate object on Z-axis (T key) with collision detection
rotateObjectZ = function()
	-- Play turn sound immediately before any checks
	playGhostSound(TURN_SOUND_ID)

	if not (isPlacing or isAdjusting) or not ghostModel then
		print("Cannot rotate Z: isPlacing =", isPlacing, "isAdjusting =", isAdjusting, "ghostModel =", ghostModel)
		return
	end

	-- Block rotation if another rotation is in progress
	if isRotating() then
		print("Cannot rotate Z: waiting for current rotation to finish")
		return
	end

	-- Calculate new rotation by applying a Z-axis rotation to the current rotation
	local zRotationStep = CFrame.fromAxisAngle(Vector3.new(0, 0, 1), math.rad(ROTATION_STEP))
	local newRotationCFrame = currentRotationCFrame * zRotationStep
	
	-- Store original rotation for potential revert
	local originalRotationCFrame = targetRotationCFrame
	targetRotationCFrame = newRotationCFrame
	
	-- Update ghost rotation to test collision
	if ghostModel and ghostModel.PrimaryPart then
		local currentPosition = ghostModel.PrimaryPart.Position

		-- Recalculate Y position to ensure hitbox touches surface after rotation
		local adjustedPosition = currentPosition

		-- Find the surface below the current X,Z position
		local validSurfaces = getValidPlacementSurfaces()
		if #validSurfaces > 0 then
			local raycastParams = RaycastParams.new()
			raycastParams.FilterType = Enum.RaycastFilterType.Include
			raycastParams.FilterDescendantsInstances = validSurfaces

			-- Cast ray from above current position downward
			local rayOrigin = Vector3.new(currentPosition.X, currentPosition.Y + 50, currentPosition.Z)
			local rayDirection = Vector3.new(0, -100, 0)
			local raycastResult = Workspace:Raycast(rayOrigin, rayDirection, raycastParams)

			if raycastResult then
				-- Calculate proper Y position based on rotated hitbox
				local surfaceY = raycastResult.Position.Y
				local rotatedBottomOffset = getRotatedObjectBottomOffset(ghostModel, targetRotationCFrame)
				adjustedPosition = Vector3.new(currentPosition.X, surfaceY + rotatedBottomOffset, currentPosition.Z)
			end
		end

		-- Apply rotation at the properly adjusted position
		ghostModel:SetPrimaryPartCFrame(CFrame.new(adjustedPosition) * targetRotationCFrame)

		-- Update mobile fixed position if on mobile
		if isMobileDevice() and mobileFixedPosition then
			-- Apply collision avoidance only for significant collisions (>0.2 studs on all axes)
			if checkGhostCollisionAtPosition(ghostModel, adjustedPosition) then
				adjustedPosition = findClosestValidPosition(ghostModel, adjustedPosition)
			end
			mobileFixedPosition = adjustedPosition
		end

		-- Always proceed with rotation tween
		print("Rotating Z (relative rotation)")
		createRotationTween()

		-- Update ghost color based on placement validity after rotation
		local hasCollision = checkGhostCollisionAtPosition(ghostModel, adjustedPosition)
		local playersInArea = arePlayersInPlacementAreaAtPosition(ghostModel, adjustedPosition)

		-- For rotation, use simplified bounds checking that allows edge placement
		local isWithinPlotBounds = true
		local playerPlot = getPlayerPlot()
		if playerPlot then
			local plotPos = playerPlot.Position
			local plotSize = playerPlot.Size
			local tolerance = 25 -- Allow edge placement
			local minX = plotPos.X - plotSize.X/2 - tolerance
			local maxX = plotPos.X + plotSize.X/2 + tolerance
			local minZ = plotPos.Z - plotSize.Z/2 - tolerance
			local maxZ = plotPos.Z + plotSize.Z/2 + tolerance

			isWithinPlotBounds = (adjustedPosition.X >= minX and adjustedPosition.X <= maxX and
			                     adjustedPosition.Z >= minZ and adjustedPosition.Z <= maxZ)
		end

		local canPlace = not hasCollision and isWithinPlotBounds and not playersInArea
		setGhostColor(ghostModel, canPlace)
	else
		-- No collision checking possible, allow rotation
		print("Rotating Z (relative rotation, no collision check)")
		createRotationTween()
	end
end

-- Start placement mode
local function startPlacement(objectName)
	if isPlacing then
		stopPlacement()
	end

	-- Get the object from ReplicatedStorage/Objects
	local objectsFolder = ReplicatedStorage:FindFirstChild("Objects")
	if not objectsFolder then
		warn("Objects folder not found in ReplicatedStorage")
		return
	end

	local objectToPlace = objectsFolder:FindFirstChild(objectName)
	if not objectToPlace then
		warn("Object '" .. objectName .. "' not found in Objects folder")
		return
	end

	if not objectToPlace:IsA("Model") then
		warn("Object '" .. objectName .. "' is not a Model")
		return
	end

	-- Check if player has this object in inventory
	local objects = player:FindFirstChild("Objects")
	if objects then
		local objectValue = objects:FindFirstChild(objectName)
		if not objectValue or objectValue.Value <= 0 then
			warn("You don't have any " .. objectName .. " to place!")
			showPlacementError("You don't have any " .. objectName .. " in your inventory!")
			shakeGhost() -- Shake and play error sound
			return
		end
	else
		warn("Objects inventory not found!")
		showPlacementError("Your inventory could not be found!")
		shakeGhost() -- Shake and play error sound
		return
	end

	-- Start placement
	isPlacing = true
	currentObject = objectToPlace

	-- Restore saved rotation for this object, or reset if no saved rotation
	local objectName = getObjectNameForRotation()
	if not restoreObjectRotation(objectName) then
		-- No saved rotation found, reset to default
		resetRotation()
		print(string.format("No saved rotation for %s, using default", objectName or "unknown"))
	end

	-- Reset position for new placement
	resetPosition()

	-- Create ghost
	ghostModel = createGhost(objectToPlace)
	
	-- Connect mouse movement
	placementConnection = RunService.Heartbeat:Connect(updateGhostPosition)
	
	-- Connect input handling
	inputConnection = UserInputService.InputBegan:Connect(function(input, gameProcessed)
		print("Input detected:", input.UserInputType, "gameProcessed =", gameProcessed, "isMobile =", isMobileDevice())
		
		-- Handle mobile touch input
		if isMobileDevice() and input.UserInputType == Enum.UserInputType.Touch then
			print("Mobile touch detected")
			-- On mobile, check if touch is on ghost object to start dragging
			if not gameProcessed then
				local touchPos = Vector2.new(input.Position.X, input.Position.Y)
				
				-- Check if touch is on mobile control UI to prevent dragging when pressing buttons
				local touchOnUI = false
				if mobileFrame and mobileFrame.Visible then
					local framePos = mobileFrame.AbsolutePosition
					local frameSize = mobileFrame.AbsoluteSize
					
					-- Check if touch is within the mobile frame bounds
					if touchPos.X >= framePos.X and touchPos.X <= framePos.X + frameSize.X and
					   touchPos.Y >= framePos.Y and touchPos.Y <= framePos.Y + frameSize.Y then
						touchOnUI = true
						print("Mobile: Touch detected on UI, ignoring for drag")
					end
					
					-- Also check individual buttons for more precise detection
					local buttons = {"Cancel", "Rotate", "Turn", "Place"}
					for _, buttonName in ipairs(buttons) do
						local button = mobileFrame:FindFirstChild(buttonName)
						if button then
							local btnPos = button.AbsolutePosition
							local btnSize = button.AbsoluteSize
							if touchPos.X >= btnPos.X and touchPos.X <= btnPos.X + btnSize.X and
							   touchPos.Y >= btnPos.Y and touchPos.Y <= btnPos.Y + btnSize.Y then
								touchOnUI = true
								print("Mobile: Touch detected on", buttonName, "button, ignoring for drag")
								break
							end
						end
					end
				end
				
				-- Only start dragging if not touching UI and not recently pressed a button
				local timeSinceButtonPress = tick() - lastButtonPressTime
				if not touchOnUI and timeSinceButtonPress > 0.1 then
					-- Check if touch is on the ghost object
					if isTouchOnGhostObject(touchPos) then
						-- Get the current ghost position (use mobileFixedPosition if available, otherwise get from ghost model)
						local currentGhostPosition = mobileFixedPosition
						if not currentGhostPosition and ghostModel and ghostModel.PrimaryPart then
							currentGhostPosition = ghostModel.PrimaryPart.Position
						elseif not currentGhostPosition then
							-- Fallback to a default position if ghost position is unknown
							currentGhostPosition = Vector3.new(0, 10, 0)
							print("Warning: Ghost position unknown, using fallback position")
						end

						-- Calculate the world position where the user touched
						local camera = Workspace.CurrentCamera
						local touchRay = camera:ScreenPointToRay(touchPos.X, touchPos.Y)

						-- Ensure we don't divide by zero
						if math.abs(touchRay.Direction.Y) > 0.001 then
							local touchWorldPos = touchRay.Origin + touchRay.Direction * ((currentGhostPosition.Y - touchRay.Origin.Y) / touchRay.Direction.Y)

							-- Calculate offset from ghost center to touch point
							local touchOffset = touchWorldPos - currentGhostPosition

							-- Start dragging
							isDraggingGhost = true
							dragStartPosition = touchPos
							dragStartWorldPosition = currentGhostPosition -- Store the current ghost world position
							dragTouchOffset = Vector3.new(touchOffset.X, 0, touchOffset.Z) -- Only use X,Z offset, keep Y at ghost level
							activeDragTouchPosition = touchPos -- Store the initial touch position
							print("Mobile: Started dragging ghost object at world position:", dragStartWorldPosition)
							print("Mobile: Touch offset from center:", dragTouchOffset)
						else
							print("Mobile: Cannot start drag - camera angle too horizontal")
						end
						
						-- Disable camera movement during drag
						disableCameraMovement()
						
						-- Note: Mobile controls remain enabled for movement while dragging
						
						-- Start filtering unwanted inputs during drag
						startInputFiltering()
						
						-- Connect to UserInputService to track drag movement
						if dragConnection then
							dragConnection:Disconnect()
						end
						
						dragConnection = UserInputService.InputChanged:Connect(function(dragInput, dragProcessed)
							if dragInput.UserInputType == Enum.UserInputType.Touch and isDraggingGhost then
								local currentTouchPos = Vector2.new(dragInput.Position.X, dragInput.Position.Y)

								-- Check if this touch is in a reasonable area (not on movement controls)
								-- Movement controls are typically on the left side of screen
								local screenWidth = workspace.CurrentCamera.ViewportSize.X
								local isOnLeftSide = currentTouchPos.X < screenWidth * 0.3 -- Left 30% of screen

								-- If touch is on left side (likely movement controls), ignore it
								if isOnLeftSide then
									return -- Likely movement controls, ignore
								end

								-- Update the active drag position to follow the finger smoothly
								activeDragTouchPosition = currentTouchPos

								print("Mobile: Drag movement detected at", currentTouchPos)

								-- Calculate new position from current touch coordinates
								local validSurfaces = getValidPlacementSurfaces()
								if #validSurfaces > 0 then
									local raycastParams = RaycastParams.new()
									raycastParams.FilterType = Enum.RaycastFilterType.Whitelist
									raycastParams.FilterDescendantsInstances = validSurfaces

									local camera = Workspace.CurrentCamera
									local unitRay = camera:ScreenPointToRay(currentTouchPos.X, currentTouchPos.Y)
									local raycastResult = Workspace:Raycast(unitRay.Origin, unitRay.Direction * 1000, raycastParams)

									local newFixedPosition = nil
									local isSidePlacement = false -- Track if this is side placement

									if raycastResult then
										local hitNormal = raycastResult.Normal
										local upVector = Vector3.new(0, 1, 0)
										local isUpwardFacing = hitNormal:Dot(upVector) > 0.8
										local isDownwardFacing = hitNormal:Dot(upVector) < -0.8
										local isVertical = math.abs(hitNormal:Dot(upVector)) < 0.2

										if isUpwardFacing or isDownwardFacing or isVertical then
											local hitPosition = raycastResult.Position
											local placementPosition = calculateSidePlacement(hitPosition, hitNormal, ghostModel, raycastResult.Instance)

											if isUpwardFacing then
												local objectSize = getRotatedObjectSize(ghostModel)
												newFixedPosition = snapToGridWithSize(placementPosition, objectSize)
												isSidePlacement = false -- Top placement
											elseif isDownwardFacing then
												local objectSize = getRotatedObjectSize(ghostModel)
												newFixedPosition = snapToGridWithSize(placementPosition, objectSize)
												isSidePlacement = false -- Bottom placement
											else
												-- For side placement, check if we're placing against the plot or against another object
												local hitInstance = raycastResult.Instance
												local isPlacingAgainstPlot = false

												-- Check if the hit instance is part of the player's plot
												local playerPlot = getPlayerPlot()
												if playerPlot and hitInstance then
													isPlacingAgainstPlot = (hitInstance == playerPlot or hitInstance.Parent == playerPlot)
												end

												-- Apply plot boundary constraint to prevent object from going inside plot
												local constrainedPosition = constrainSidePlacementToPlotBounds(placementPosition, ghostModel, hitInstance)

												-- Size-aware grid snapping for mobile side placement
												local objectSize = getRotatedObjectSize(ghostModel)
												newFixedPosition = snapToGridWithSize(constrainedPosition, objectSize)
												isSidePlacement = true -- Side placement
												print("Mobile side placement - Size-aware grid snap:", newFixedPosition)
											end
										end
									end

									-- If we got a valid position from raycast, use it with intelligent constraining
									if newFixedPosition then
										-- For side placement (vertical surfaces), use the calculated position directly
										-- For top/bottom placement, we can apply touch offset for better control
										if isSidePlacement then
											-- Side placement - use the calculated position directly for proper surface alignment
											print("Mobile side placement - Using calculated position:", newFixedPosition)
										else
											-- Top/bottom placement - apply touch offset for better dragging control
											if dragTouchOffset then
												-- Calculate where the current touch is in world space
												local currentRay = camera:ScreenPointToRay(currentTouchPos.X, currentTouchPos.Y)
												local planeY = newFixedPosition.Y
												if math.abs(currentRay.Direction.Y) > 0.001 then
													local currentTouchWorldPos = currentRay.Origin + currentRay.Direction * ((planeY - currentRay.Origin.Y) / currentRay.Direction.Y)
													-- Adjust the ghost position to maintain the touch offset
													local offsetPosition = currentTouchWorldPos - dragTouchOffset

													-- Apply grid snapping to the offset position
													local objectSize = getRotatedObjectSize(ghostModel)
													newFixedPosition = snapToGridWithSize(offsetPosition, objectSize)
													print("Mobile top/bottom placement - Applied grid snap to offset position:", newFixedPosition)
												end
											end
										end

										-- Use direction-preserving constraint instead of simple clamping
										newFixedPosition = constrainPositionToPlotBoundsWithDirection(newFixedPosition, mobileFixedPosition, ghostModel)
										mobileFixedPosition = newFixedPosition
									else
										-- No valid surface hit - try to follow drag direction within plot bounds
										if dragStartWorldPosition and dragStartPosition and dragTouchOffset then
											-- Calculate world space position where current touch should place the ghost center
											local currentRay = camera:ScreenPointToRay(currentTouchPos.X, currentTouchPos.Y)

											-- Project current touch onto a horizontal plane at the ghost height
											local planeY = dragStartWorldPosition.Y

											-- Ensure we don't divide by zero
											if math.abs(currentRay.Direction.Y) > 0.001 then
												local currentTouchWorldPos = currentRay.Origin + currentRay.Direction * ((planeY - currentRay.Origin.Y) / currentRay.Direction.Y)

												-- Calculate where the ghost center should be based on the touch offset
												-- If user grabbed the corner, the center should be offset from the touch point
												local offsetPosition = currentTouchWorldPos - dragTouchOffset

												-- Apply grid snapping to the offset position
												local objectSize = getRotatedObjectSize(ghostModel)
												local newPosition = snapToGridWithSize(offsetPosition, objectSize)

												-- Constrain this projected position to plot bounds
												newPosition = constrainPositionToPlotBounds(newPosition, ghostModel)
												mobileFixedPosition = newPosition
												print("Mobile: Following drag with preserved touch offset and grid snap")
											else
												print("Mobile: Cannot calculate drag direction (camera angle too horizontal)")
											end
										end
									end
								end
							end
						end)
					else
						print("Mobile: Touch not on ghost object, ignoring")
					end
				end
			end
		-- Handle PC mouse click input
		elseif not isMobileDevice() and input.UserInputType == Enum.UserInputType.MouseButton1 then
			print("PC mouse click detected")
			-- PC left click - only place if not clicking UI
			print("PC: MouseButton1 detected, gameProcessed =", gameProcessed)
			if not gameProcessed then
				print("PC: Attempting to place object")
				placeObject()
			else
				print("PC: Click was processed by UI, ignoring")
			end
		end

		-- Handle keyboard inputa
		if not gameProcessed then
			-- Only process keyboard inputs if not consumed by UI
			if input.KeyCode == Enum.KeyCode.Escape or input.KeyCode == Enum.KeyCode.Q then
				-- Escape or Q - cancel placement
				stopPlacement()
			elseif input.KeyCode == Enum.KeyCode.R then
				-- R key - rotate on Y-axis
				rotateObjectY()
			elseif input.KeyCode == Enum.KeyCode.T then
				-- T key - rotate on Z-axis
				rotateObjectZ()
			end
		end
	end)
	
	-- Connect input ending to stop dragging
	inputEndedConnection = UserInputService.InputEnded:Connect(function(input, gameProcessed)
		if isMobileDevice() and input.UserInputType == Enum.UserInputType.Touch and isDraggingGhost then
			-- Check if this touch end is not on the left side (movement controls)
			local endTouchPos = Vector2.new(input.Position.X, input.Position.Y)
			local screenWidth = workspace.CurrentCamera.ViewportSize.X
			local isOnLeftSide = endTouchPos.X < screenWidth * 0.3 -- Left 30% of screen

			-- If touch end is not on left side, it might be the drag ending
			if not isOnLeftSide then
				-- Stop dragging when any non-movement touch ends
				isDraggingGhost = false
				dragStartPosition = nil
				dragStartWorldPosition = nil
				dragTouchOffset = nil
				activeDragTouchPosition = nil

				-- Re-enable camera movement
				enableCameraMovement()

				-- Note: Mobile controls were never disabled

				-- Stop input filtering
				stopInputFiltering()

				-- Disconnect drag movement tracking
				if dragConnection then
					dragConnection:Disconnect()
					dragConnection = nil
				end

				print("Mobile: Stopped dragging ghost object")
			end
		end
		end)
	
	-- Add move buttons for mobile if needed (optional enhancement)
	-- Mobile uses fixed positioning - user touches screen to set new position
	
	-- Show appropriate control UI and setup mobile buttons
	showControlUI()
	if isMobileDevice() then
		setupMobileButtons()
	end
	
	-- Setup tool monitoring to cancel placement if tool is unequipped
	setupToolMonitoring()
end

-- Make startPlacement available globally immediately after definition
_G.StartPlacement = startPlacement
print("DEBUG: StartPlacement function made globally available")

-- Place the object (handles both placement and adjustment)
placeObject = function()
	print("placeObject() called - isPlacing =", isPlacing, "isAdjusting =", isAdjusting, "ghostModel =", ghostModel, "currentObject =", currentObject)

	-- Check if we're in placement or adjustment mode
	if not (isPlacing or isAdjusting) or not ghostModel or not currentObject then
		print("placeObject() early return - missing requirements")
		return
	end

	-- Prevent placement during shake animation
	if isShaking then
		print("Cannot place object - shake animation in progress")
		return
	end

	-- Check if rotation is in progress
	if isRotating() then
		-- Only queue if not already queued
		if not placementQueued then
			print("Rotation in progress, queuing placement for after rotation completes")
			placementQueued = true
		else
			print("Placement already queued, ignoring additional placement request")
		end
		
		return
	end
	
	local validSurfaces = getValidPlacementSurfaces()
	if #validSurfaces == 0 then
		warn("Cannot place object - no valid surfaces found!")
		showPlacementError("No valid surfaces found for placement!")
		shakeGhost() -- Shake the ghost and play error sound
		return
	end
	
	local finalPosition
	local isPlacementAgainstPlot = false -- Track if placing against plot for validation

	-- USE GHOST'S CURRENT POSITION (already adjusted for collisions) WITH GRID SNAPPING
	if ghostModel and ghostModel.PrimaryPart then
		local ghostPosition = ghostModel.PrimaryPart.Position
		-- Apply grid snapping to ensure proper alignment
		local objectSize = getRotatedObjectSize(currentObject)
		finalPosition = snapToGridWithSize(ghostPosition, objectSize)
		print("Using ghost's current position with grid snap for placement:", finalPosition)
	elseif isMobileDevice() and mobileFixedPosition then
		-- Fallback: On mobile, use the fixed position if available
		local objectSize = getRotatedObjectSize(currentObject)
		finalPosition = snapToGridWithSize(mobileFixedPosition, objectSize)
		print("Using mobile fixed position with grid snap for placement:", finalPosition)
	else
		warn("Cannot place object - no valid position available!")
		showPlacementError("No valid position available for placement!")
		shakeGhost() -- Shake the ghost and play error sound
		return
	end
	
	-- Check for collisions before placement
	local hasCollision = checkGhostCollisionAtPosition(ghostModel, finalPosition)
	if hasCollision then
		warn("Cannot place object - collision detected!")
		showPlacementError("The placement would collide with another object!")
		shakeGhost() -- Shake the ghost and play error sound
		return
	end
	
	-- Check if players would be in the placement area
	local playersInArea = arePlayersInPlacementAreaAtPosition(ghostModel, finalPosition)
	if playersInArea then
		warn("Cannot place object - players are in the placement area!")
		showPlacementError("Players are standing in the placement area!")
		shakeGhost() -- Shake the ghost and play error sound
		return
	end
	
	if isAdjusting then
		-- For adjustment mode, send the adjustment to the server
		local rotationCFrame = getCurrentRotationCFrame()
		local finalCFrame = CFrame.new(finalPosition) * rotationCFrame

		-- Save the rotation for this object type before sending adjustment
		local objectName = getObjectNameForRotation()
		saveObjectRotation(objectName)

		-- Fire event to server to adjust the object
		local adjustEvent = ReplicatedStorage:WaitForChild("Events"):WaitForChild("AdjustObject")
		if originalObject and originalObjectName then
			-- Send the original object name (server still has it in plot folder) and new CFrame
			print("DEBUG: About to send adjustment request")
			print("DEBUG: originalObjectName:", originalObjectName)
			print("DEBUG: finalCFrame:", finalCFrame)
			adjustEvent:FireServer(originalObjectName, finalCFrame)
			print("Sent adjustment request to server for:", originalObjectName)

			-- Don't call stopPlacement() yet - wait for server response
			-- The server response handler will clean up properly
		else
			print("DEBUG: Cannot send adjustment - missing data")
			print("DEBUG: originalObject:", originalObject)
			print("DEBUG: originalObjectName:", originalObjectName)
		end
	elseif isPlacing then
		-- Regular placement mode - check inventory and place new object
		local objects = player:FindFirstChild("Objects")
		if objects then
			local objectValue = objects:FindFirstChild(currentObject.Name)
			if not objectValue or objectValue.Value <= 0 then
				warn("You don't have any " .. currentObject.Name .. " to place!")
				showPlacementError("You don't have any " .. currentObject.Name .. " in your inventory!")
				shakeGhost() -- Shake and play error sound
				return
			end
		end

		-- Save the rotation for this object type before placing
		local objectName = getObjectNameForRotation()
		saveObjectRotation(objectName)

		-- Fire event to server to place the object
		local event = ReplicatedStorage:WaitForChild("Events"):WaitForChild("PlaceObject")
		local rotationCFrame = getCurrentRotationCFrame()
		local finalCFrame = CFrame.new(finalPosition) * rotationCFrame
		event:FireServer(currentObject.Name, finalCFrame)

		-- Play placement sound at the placement position with pitch based on object size
		playPlacementSound(finalPosition, currentObject)
	end
	
	-- Unequip the Blueprints tool after successful placement
	local player = Players.LocalPlayer
	print("Attempting to unequip Blueprints tool after placement...")
	if player.Character then
		print("Player character found")
		local humanoid = player.Character:FindFirstChild("Humanoid")
		if humanoid then
			print("Humanoid found")
			-- Tools are children of the Character, not the Humanoid
			local equippedTool = player.Character:FindFirstChild("Blueprints")
			if equippedTool then
				print("Blueprints tool found, unequipping...")
				humanoid:UnequipTools()
				print("Unequipped Blueprints tool after successful placement")
			else
				print("Blueprints tool not found in character")
				-- Debug: print all tools in character
				print("Tools in character:")
				for _, child in pairs(player.Character:GetChildren()) do
					if child:IsA("Tool") then
						print("  - " .. child.Name)
					end
				end
			end
		else
			print("Humanoid not found")
		end
	else
		print("Player character not found")
	end
end

-- Stop placement mode
stopPlacement = function()
	isPlacing = false
	currentObject = nil
	placementQueued = false -- Clear any queued placement
	
	-- Clean up rotation tween
	if rotationTween then
		rotationTween:Cancel()
		rotationTween = nil
	end
	
	-- Reset position state
	resetPosition()
	
	-- Clean up ghost
	if ghostModel then
		ghostModel:Destroy()
		ghostModel = nil
	end
	
	-- Disconnect connections
	if placementConnection then
		placementConnection:Disconnect()
		placementConnection = nil
	end
	
	if inputConnection then
		inputConnection:Disconnect()
		inputConnection = nil
	end
	
	if inputEndedConnection then
		inputEndedConnection:Disconnect()
		inputEndedConnection = nil
	end
	
	-- Hide control UI and cleanup mobile buttons
	hideControlUI()
	cleanupMobileButtons()
	
	-- Cleanup tool monitoring
	cleanupToolMonitoring()
	
	-- Unequip the Blueprints tool when cancelling placement
	local player = Players.LocalPlayer
	if player.Character then
		local humanoid = player.Character:FindFirstChild("Humanoid")
		if humanoid then
			local equippedTool = player.Character:FindFirstChild("Blueprints")
			if equippedTool then
				humanoid:UnequipTools()
				print("Unequipped Blueprints tool after cancelling placement")
			end
		end
	end
end

-- Make stopPlacement available globally immediately
_G.StopPlacement = stopPlacement

-- ========================================
-- ADJUST TOOL FUNCTIONS
-- ========================================

-- Get object under mouse for adjustment
local function getAdjustObjectUnderMouse()
	local mouse = player:GetMouse()
	local camera = workspace.CurrentCamera

	-- Create a ray from the camera through the mouse position
	local unitRay = camera:ScreenPointToRay(mouse.X, mouse.Y)

	-- Create raycast params to only hit objects in the player's plot
	local raycastParams = RaycastParams.new()
	raycastParams.FilterType = Enum.RaycastFilterType.Include

	-- Get all objects in player's plot
	local validObjects = {}
	local plots = Workspace:FindFirstChild("Plots")
	if plots then
		for _, plotFolder in pairs(plots:GetChildren()) do
			local values = plotFolder:FindFirstChild("Values")
			local ownerId = values and values:FindFirstChild("OwnerId")

			if ownerId and ownerId.Value == player.UserId then
				local objectsFolder = plotFolder:FindFirstChild("Objects")
				if objectsFolder then
					for _, placedObject in pairs(objectsFolder:GetChildren()) do
						if placedObject:IsA("Model") and placedObject ~= originalObject then
							-- Add all parts of the object to raycast filter
							for _, part in pairs(placedObject:GetDescendants()) do
								if part:IsA("BasePart") then
									table.insert(validObjects, part)
								end
							end
						end
					end
				end
				break
			end
		end
	end

	if #validObjects == 0 then
		return nil
	end

	raycastParams.FilterDescendantsInstances = validObjects

	-- Perform raycast
	local raycastResult = workspace:Raycast(unitRay.Origin, unitRay.Direction * 1000, raycastParams)

	if raycastResult then
		-- Find the model that contains this part
		local hitPart = raycastResult.Instance
		local model = hitPart.Parent

		-- Make sure we found a valid model
		if model and model:IsA("Model") and model.Parent and model.Parent.Name == "Objects" then
			return model
		end
	end

	return nil
end

-- Create highlight for adjust tool with tweening (uses ghost highlight style)
local function createAdjustHighlight(object, mode, transparency)
	if not object or not object:IsA("Model") then return end

	-- Remove existing highlight
	local existingHighlight = object:FindFirstChild("AdjustHighlight")
	if existingHighlight then
		existingHighlight:Destroy()
	end

	-- Create new highlight (same style as ghost highlight)
	local highlight = Instance.new("Highlight")
	highlight.Name = "AdjustHighlight"

	-- Use ghost highlight colors by default, with red for delete mode
	if mode == "delete" then
		highlight.FillColor = Color3.fromRGB(255, 52, 52) -- Red for delete
		highlight.OutlineColor = Color3.fromRGB(255, 100, 100) -- Lighter red outline
	else
		-- Default to ghost highlight colors (for hover, move, etc.)
		highlight.FillColor = Color3.fromRGB(52, 89, 255) -- Same as ghost highlight
		highlight.OutlineColor = Color3.fromRGB(106, 171, 255) -- Same as ghost highlight
	end

	highlight.FillTransparency = 1 -- Start fully transparent
	highlight.OutlineTransparency = 1 -- Start fully transparent
	highlight.DepthMode = Enum.HighlightDepthMode.Occluded -- Same as ghost highlight (not AlwaysOnTop)
	highlight.Parent = object

	-- Tween the highlight in (same style as ghost highlight)
	local targetTransparency = transparency or 0.6 -- Same as ghost highlight
	local tweenInfo = TweenInfo.new(0.2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
	local tween = TweenService:Create(highlight, tweenInfo, {
		FillTransparency = targetTransparency,
		OutlineTransparency = 0 -- Same as ghost highlight (fully opaque outline)
	})
	tween:Play()

	return highlight
end

-- Remove adjust highlight from an object with fade-out tween
local function removeAdjustHighlight(object)
	if not object then return end

	local highlight = object:FindFirstChild("AdjustHighlight")
	if highlight then
		-- Tween the highlight out
		local tweenInfo = TweenInfo.new(0.15, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
		local tween = TweenService:Create(highlight, tweenInfo, {
			FillTransparency = 1,
			OutlineTransparency = 1
		})
		tween:Play()

		-- Destroy after tween completes
		tween.Completed:Connect(function()
			if highlight and highlight.Parent then
				highlight:Destroy()
			end
		end)
	end
end

-- Handle hover highlighting when Adjust tool is equipped
local function handleAdjustHover()
	if not adjustToolEquipped or isAdjusting then return end

	-- Skip hover highlighting on mobile devices
	if isMobileDevice() then return end

	local objectUnderMouse = getAdjustObjectUnderMouse()

	-- Update hovered object (don't highlight if it's already selected)
	if objectUnderMouse ~= adjustHoveredObject then
		-- Remove highlight from previous object (only if it's not selected)
		if adjustHoveredObject and adjustHoveredObject ~= adjustSelectedObject then
			removeAdjustHighlight(adjustHoveredObject)
		end

		-- Add highlight to new object (only if it's not already selected)
		adjustHoveredObject = objectUnderMouse
		if adjustHoveredObject and adjustHoveredObject ~= adjustSelectedObject then
			print("Adjust tool: Highlighting object:", adjustHoveredObject.Name)
			createAdjustHighlight(adjustHoveredObject) -- Use ghost highlight style
		end
	end
end




-- Handle tool activation (left click)
local function onAdjustToolActivated()
	-- Don't allow interactions while adjusting
	if isAdjusting then
		print("Adjust tool: Cannot interact with objects while adjusting")
		return
	end

	-- Skip on mobile devices (they use tap-based interaction)
	if isMobileDevice() then
		print("Adjust tool: Skipping tool activation on mobile device")
		return
	end

	print("Adjust tool: Tool activated (PC)")

	local objectUnderMouse = getAdjustObjectUnderMouse()
	print("Adjust tool: Object under mouse:", objectUnderMouse and objectUnderMouse.Name or "nil")

	if objectUnderMouse then
		if adjustSelectedObject == objectUnderMouse then
			-- Second click on same object: start adjustment
			print("Adjust tool: Starting adjustment for:", objectUnderMouse.Name)
			-- Fire event to placement script
			local adjustToolEvent = ReplicatedStorage:FindFirstChild("AdjustToolEvent")
			if not adjustToolEvent then
				-- Create the event if it doesn't exist
				adjustToolEvent = Instance.new("BindableEvent")
				adjustToolEvent.Name = "AdjustToolEvent"
				adjustToolEvent.Parent = ReplicatedStorage
				print("Adjust tool: Created AdjustToolEvent")
			end
			adjustToolEvent:Fire(objectUnderMouse)
			-- Clear selection (isAdjusting will be set by startAdjustment)
			if adjustSelectedObject then
				removeAdjustHighlight(adjustSelectedObject)
			end
			adjustSelectedObject = nil
			adjustSelectedMode = nil
		else
			-- First click: select object for confirmation
			if adjustSelectedObject then
				-- Remove highlight from previously selected object
				removeAdjustHighlight(adjustSelectedObject)
			end
			adjustSelectedObject = objectUnderMouse
			adjustSelectedMode = "move" -- Track that this object was selected for move
			-- Add blue highlight to show it's selected for adjustment (same transparency as hover)
			createAdjustHighlight(adjustSelectedObject, "move", 0.6) -- Same transparency as hover
			print("Adjust tool: Object selected for adjustment (click again to start adjusting):", adjustSelectedObject.Name)
		end
	else
		-- Clicked on empty space: clear selection
		if adjustSelectedObject then
			removeAdjustHighlight(adjustSelectedObject)
			adjustSelectedObject = nil
			adjustSelectedMode = nil
			print("Adjust tool: Selection cleared")
		else
			print("Adjust tool: No valid object under mouse")
		end
	end
end

-- Clear mobile tap states when mode changes
local function clearMobileTapStates()
	for objectName, _ in pairs(mobileTapStates) do
		local object = workspace:FindFirstChild(objectName, true)
		if object then
			removeAdjustHighlight(object)
		end
	end
	mobileTapStates = {}
	if adjustSelectedObject then
		removeAdjustHighlight(adjustSelectedObject)
		adjustSelectedObject = nil
		adjustSelectedMode = nil
	end
	print("Adjust tool: Cleared all mobile tap states")
end

-- Handle mobile tap on object
local function handleMobileTap(touchedObject, adjustMode)
	-- Don't allow interactions while adjusting
	if isAdjusting then
		print("Adjust tool: Cannot interact with objects while adjusting")
		return
	end

	local objectName = touchedObject.Name
	local currentState = mobileTapStates[objectName]
	local currentTime = tick()

	-- If mode changed or this is a new object, reset state
	if not currentState or currentState.mode ~= adjustMode then
		-- Clear previous selection if different object
		if adjustSelectedObject and adjustSelectedObject ~= touchedObject then
			removeAdjustHighlight(adjustSelectedObject)
			mobileTapStates[adjustSelectedObject.Name] = nil
		end

		-- Initialize new state with current time
		mobileTapStates[objectName] = {tapCount = 1, mode = adjustMode, lastTapTime = currentTime}
		adjustSelectedObject = touchedObject

		if adjustMode == "Move" then
			-- First tap in Move mode: blue highlight (ghost style)
			createAdjustHighlight(touchedObject, "move", 0.6) -- Same transparency as hover
			print("Adjust tool: MOVE MODE - First tap, blue highlight on:", objectName)
		elseif adjustMode == "Remove" then
			-- First tap in Remove mode: red highlight (tap again to delete)
			createAdjustHighlight(touchedObject, "delete", 0.6) -- Same transparency as hover
			print("Adjust tool: REMOVE MODE - First tap, red highlight on (tap again to delete):", objectName)
		end
		return
	end

	-- Check if enough time has passed since last tap (debounce)
	if currentTime - currentState.lastTapTime < TAP_DEBOUNCE_TIME then
		print("Adjust tool: Tap too fast, ignoring (debounce). Time since last tap:", currentTime - currentState.lastTapTime)
		return
	end

	-- Same object and mode, increment tap count and update time
	currentState.tapCount = currentState.tapCount + 1
	currentState.lastTapTime = currentTime

	if adjustMode == "Move" then
		if currentState.tapCount == 2 then
			-- Second tap in Move mode: start adjustment
			print("Adjust tool: MOVE MODE - Second tap, starting adjustment for:", objectName)
			local adjustToolEvent = ReplicatedStorage:FindFirstChild("AdjustToolEvent")
			if not adjustToolEvent then
				adjustToolEvent = Instance.new("BindableEvent")
				adjustToolEvent.Name = "AdjustToolEvent"
				adjustToolEvent.Parent = ReplicatedStorage
			end

			-- Add a small delay to prevent the placement script from processing the same touch event
			task.spawn(function()
				task.wait(0.05) -- 50ms delay
				adjustToolEvent:Fire(touchedObject)
			end)

			-- Clear selection (isAdjusting will be set by startAdjustment)
			removeAdjustHighlight(touchedObject)
			adjustSelectedObject = nil
			adjustSelectedMode = nil
			mobileTapStates[objectName] = nil
		end

	elseif adjustMode == "Remove" then
		if currentState.tapCount >= 2 then
			-- Second tap in Remove mode: delete object with smooth animation
			print("Adjust tool: REMOVE MODE - Second tap, deleting object:", objectName)
			smoothDeleteObject(touchedObject, objectName)
			removeAdjustHighlight(touchedObject)
			adjustSelectedObject = nil
			adjustSelectedMode = nil
			mobileTapStates[objectName] = nil
		end
	end
end

-- Get current mobile adjust mode from placement script
local function getMobileAdjustMode()
	-- Always get fresh value from the StringValue (no caching)
	local modeValue = ReplicatedStorage:FindFirstChild("MobileAdjustModeValue")
	if modeValue and modeValue:IsA("StringValue") then
		local currentMode = modeValue.Value
		print("Adjust tool: Retrieved mobile adjust mode:", currentMode, "at time:", tick())

		-- Extra validation to ensure we have a valid mode
		if currentMode == "Move" or currentMode == "Remove" then
			print("Adjust tool: Valid mode confirmed:", currentMode)
			return currentMode
		else
			print("Adjust tool: Invalid mode value:", currentMode, "- defaulting to Move")
			return "Move"
		end
	else
		print("Adjust tool: MobileAdjustModeValue not found, defaulting to Move")
		-- Try waiting for it to be created by the placement script
		modeValue = ReplicatedStorage:WaitForChild("MobileAdjustModeValue", 1)
		if modeValue and modeValue:IsA("StringValue") then
			local currentMode = modeValue.Value
			print("Adjust tool: Retrieved mobile adjust mode after wait:", currentMode)
			if currentMode == "Move" or currentMode == "Remove" then
				return currentMode
			end
		end
		return "Move" -- Default mode
	end
end

-- Handle input (R key for delete and mobile touch)
local function onAdjustInput(input, gameProcessed)
	if gameProcessed or not adjustToolEquipped then return end

	-- Don't handle mobile touch input when already adjusting - let placement script handle it
	if isAdjusting and isMobileDevice() and input.UserInputType == Enum.UserInputType.Touch then
		return
	end

	-- Handle mobile touch input with confirmation system (like PC)
	if isMobileDevice() and input.UserInputType == Enum.UserInputType.Touch then
		print("Adjust tool: Mobile touch detected")
		local touchPos = Vector2.new(input.Position.X, input.Position.Y)

		-- Check if touch is on left side of screen (movement controls) - ignore for remove mode
		local screenWidth = workspace.CurrentCamera.ViewportSize.X
		local isOnLeftSide = touchPos.X < screenWidth * 0.3 -- Left 30% of screen
		local currentAdjustMode = getMobileAdjustMode()

		if isOnLeftSide and currentAdjustMode == "Remove" then
			print("Adjust tool: Touch on left side during Remove mode, ignoring to prevent walking interference")
			return
		end

		-- Check if we're in cooldown period after button press
		local currentTime = tick()
		if currentTime - lastButtonPressTime < BUTTON_PRESS_COOLDOWN then
			print("Adjust tool: In button press cooldown, ignoring touch")
			return
		end

		-- Check if touch is on mobile adjust UI to prevent object selection when pressing buttons
		local touchOnUI = false
		local playerGui = player:WaitForChild("PlayerGui")
		local controlUI = playerGui:FindFirstChild("ControlUI")
		if controlUI then
			local mobileAdjustFrame = controlUI:FindFirstChild("MobileAdjustFrame")
			if mobileAdjustFrame and mobileAdjustFrame.Visible then
				-- Check individual buttons first for more precise detection
				local moveButton = mobileAdjustFrame:FindFirstChild("Move")
				local removeButton = mobileAdjustFrame:FindFirstChild("Remove")

				local buttons = {moveButton, removeButton}
				for _, button in ipairs(buttons) do
					if button then
						local btnPos = button.AbsolutePosition
						local btnSize = button.AbsoluteSize
						if touchPos.X >= btnPos.X and touchPos.X <= btnPos.X + btnSize.X and
						   touchPos.Y >= btnPos.Y and touchPos.Y <= btnPos.Y + btnSize.Y then
							touchOnUI = true
							lastButtonPressTime = currentTime -- Update button press time
							print("Adjust tool: Touch detected on", button.Name, "button, ignoring for object selection")
							break
						end
					end
				end

				-- If not on individual buttons, check frame bounds as fallback
				if not touchOnUI then
					local framePos = mobileAdjustFrame.AbsolutePosition
					local frameSize = mobileAdjustFrame.AbsoluteSize

					-- Check if touch is within the mobile adjust frame bounds
					if touchPos.X >= framePos.X and touchPos.X <= framePos.X + frameSize.X and
					   touchPos.Y >= framePos.Y and touchPos.Y <= framePos.Y + frameSize.Y then
						touchOnUI = true
						print("Adjust tool: Touch detected on mobile adjust frame, ignoring for object selection")
					end
				end
			end
		end

		-- Only process object selection if not touching UI
		if not touchOnUI then
			local touchedObject = getObjectUnderTouch(touchPos)

			if touchedObject then
				-- Get current mobile adjust mode
				local adjustMode = getMobileAdjustMode()
				print("Adjust tool: Touched object:", touchedObject.Name, "Current mode:", adjustMode)

				-- Handle the tap based on current mode
				handleMobileTap(touchedObject, adjustMode)
			else
				-- Tapped on empty space: clear all tap states
				clearMobileTapStates()
				print("Adjust tool: Tapped empty space, cleared all selections")
			end
		end

		return -- Don't process other input types on mobile
	end

	-- Handle PC keyboard input (R key for delete)
	if input.KeyCode == Enum.KeyCode.R then
		-- Skip on mobile devices
		if isMobileDevice() then
			return
		end

		-- Don't allow interactions while adjusting
		if isAdjusting then
			print("Adjust tool: Cannot delete objects while adjusting")
			return
		end

		local objectUnderMouse = getAdjustObjectUnderMouse()
		print("Adjust tool: R key pressed, object under mouse:", objectUnderMouse and objectUnderMouse.Name or "nil")

		if objectUnderMouse then
			-- Check if this is the same object selected for a different mode or first R press
			if adjustSelectedObject ~= objectUnderMouse or adjustSelectedMode ~= "delete" then
				-- Remove previous selection
				if adjustSelectedObject then
					removeAdjustHighlight(adjustSelectedObject)
				end
				adjustSelectedObject = objectUnderMouse
				adjustSelectedMode = "delete" -- Track that this object was selected for delete
				createAdjustHighlight(adjustSelectedObject, "delete", 0.6) -- Red highlight with same transparency as hover
				print("Adjust tool: Object selected for deletion (press R again to confirm):", adjustSelectedObject.Name)
			else
				-- Second R press on same object already selected for delete: delete object with smooth animation
				if adjustSelectedObject and adjustSelectedMode == "delete" then
					print("Adjust tool: Deleting object:", adjustSelectedObject.Name)
					-- Start smooth deletion animation
					smoothDeleteObject(adjustSelectedObject, adjustSelectedObject.Name)
					-- Clear selection (server will handle the actual deletion after animation)
					removeAdjustHighlight(adjustSelectedObject)
					adjustSelectedObject = nil
					adjustSelectedMode = nil
				end
			end
		else
			-- R pressed but no object under mouse - clear any existing selection
			if adjustSelectedObject then
				removeAdjustHighlight(adjustSelectedObject)
				adjustSelectedObject = nil
				adjustSelectedMode = nil
				print("Adjust tool: R pressed with no object under mouse, cleared selection")
			end
		end
	end
end

-- Note: getMobileAdjustMode function is defined earlier in the file



-- Start adjustment mode for the selected object
local function startAdjustment(object)
	if not object or isPlacing or isAdjusting then return end

	print("Adjust tool: Starting adjustment for:", object.Name)

	-- Set adjustment state and store original
	isAdjusting = true
	originalObject = object

	-- Store the original position for potential reversion
	originalObjectPosition = object.PrimaryPart and object.PrimaryPart.CFrame or object:GetModelCFrame()

	-- Store original object name and parent for server communication and restoration
	originalObjectName = object.Name
	originalObjectParent = object.Parent
	print("DEBUG: Stored originalObjectName:", originalObjectName)
	print("DEBUG: Stored originalObjectParent:", originalObjectParent)

	-- Create a clone for the ghost (with proper transparency)
	local objectClone = object:Clone()

	-- Remove any existing highlights from the clone to prevent duplicate highlights
	for _, child in pairs(objectClone:GetDescendants()) do
		if child:IsA("Highlight") then
			child:Destroy()
		end
	end

	currentObject = objectClone

	-- Move the original object to ReplicatedStorage on CLIENT ONLY to completely remove it from collision detection
	-- This completely removes it from the plot's Objects folder on the client while preserving server state
	originalObjectParent = object.Parent
	object.Parent = ReplicatedStorage
	print("Moved original object to ReplicatedStorage on client for adjustment:", object.Name)

	-- Store the original CFrame to position the ghost
	local originalCFrame = object.PrimaryPart and object.PrimaryPart.CFrame or object:GetModelCFrame()

	-- Set both isAdjusting and isPlacing for the ghost system to work properly
	isPlacing = true

	-- Reset position state
	resetPosition()

	-- Create ghost model
	ghostModel = createGhost(objectClone)
	if not ghostModel then
		warn("Failed to create ghost model for adjustment")
		cancelAdjustment()
		return
	end
	print("DEBUG: Ghost model created successfully:", ghostModel.Name)

	-- Position ghost at original object's location
	if ghostModel and ghostModel.PrimaryPart then
		ghostModel:SetPrimaryPartCFrame(originalCFrame)
		currentPosition = originalCFrame.Position
		targetPosition = currentPosition
		lastValidPosition = currentPosition
		print("DEBUG: Positioned adjustment ghost at original location:", currentPosition)
		print("DEBUG: Ghost model parent:", ghostModel.Parent)
		print("DEBUG: Ghost model visible:", ghostModel.Parent == workspace)
	else
		warn("DEBUG: Ghost model has no PrimaryPart!")
	end

	-- Show control UI and setup mobile buttons (same as regular placement)
	showControlUI()
	if isMobileDevice() then
		setupMobileButtons()
	end

	-- Set flag to prevent immediate placement from the same click that started adjustment
	justStartedAdjustment = true
	-- Clear the flag after a short delay using spawn
	task.spawn(function()
		task.wait(0.1)
		justStartedAdjustment = false
	end)

	-- For mobile devices, allow immediate dragging after adjustment starts
	if isMobileDevice() then
		mobileAdjustmentConfirmed = true
		print("Mobile: Adjustment started, dragging enabled immediately")
	end



	-- Connect the same update system as placement
	print("DEBUG: Connecting updateGhostPosition to RunService.Heartbeat")
	placementConnection = RunService.Heartbeat:Connect(updateGhostPosition)

	-- Connect input handling (same as startPlacement)
	inputConnection = UserInputService.InputBegan:Connect(function(input, gameProcessed)
		print("Input detected:", input.UserInputType, "gameProcessed =", gameProcessed, "isMobile =", isMobileDevice())

		-- Handle mobile touch input
		if isMobileDevice() and input.UserInputType == Enum.UserInputType.Touch then
			print("Mobile touch detected")

			-- Ignore touch events briefly after adjustment just started
			if adjustmentJustStarted then
				print("Mobile: Ignoring touch - adjustment just started")
				return
			end

			-- On mobile, check if touch is on ghost object to start dragging
			if not gameProcessed then
				local touchPos = Vector2.new(input.Position.X, input.Position.Y)

				-- Check if touch is on mobile control UI to prevent dragging when pressing buttons
				local touchOnUI = false
				local mobileFrame = player.PlayerGui:FindFirstChild("MobileControls")
				if mobileFrame and mobileFrame.Visible then
					local framePos = mobileFrame.AbsolutePosition
					local frameSize = mobileFrame.AbsoluteSize

					-- Check if touch is within the mobile frame bounds
					if touchPos.X >= framePos.X and touchPos.X <= framePos.X + frameSize.X and
					   touchPos.Y >= framePos.Y and touchPos.Y <= framePos.Y + frameSize.Y then
						touchOnUI = true
						print("Mobile: Touch detected on UI, ignoring for drag")
					end

					-- Also check individual buttons for more precise detection
					local buttons = {"Cancel", "Rotate", "Turn", "Place"}
					for _, buttonName in ipairs(buttons) do
						local button = mobileFrame:FindFirstChild(buttonName)
						if button then
							local btnPos = button.AbsolutePosition
							local btnSize = button.AbsoluteSize
							if touchPos.X >= btnPos.X and touchPos.X <= btnPos.X + btnSize.X and
							   touchPos.Y >= btnPos.Y and touchPos.Y <= btnPos.Y + btnSize.Y then
								touchOnUI = true
								print("Mobile: Touch detected on button", buttonName, "ignoring for drag")
								break
							end
						end
					end
				end

				-- Only start dragging if not touching UI and not recently pressed a button
				local timeSinceButtonPress = tick() - lastButtonPressTime
				if not touchOnUI and timeSinceButtonPress > 0.1 then
					-- For mobile adjustment mode, allow immediate dragging
					-- (confirmation requirement removed for better UX)

					-- Check if touch is on the ghost object
					if isTouchOnGhostObject(touchPos) then
						-- Get the current ghost position (use mobileFixedPosition if available, otherwise get from ghost model)
						local currentGhostPosition = mobileFixedPosition
						if not currentGhostPosition and ghostModel and ghostModel.PrimaryPart then
							currentGhostPosition = ghostModel.PrimaryPart.Position
						elseif not currentGhostPosition then
							-- Fallback to a default position if ghost position is unknown
							currentGhostPosition = Vector3.new(0, 10, 0)
							print("Warning: Ghost position unknown, using fallback position")
						end

						-- Calculate the world position where the user touched
						local camera = Workspace.CurrentCamera
						local touchRay = camera:ScreenPointToRay(touchPos.X, touchPos.Y)

						-- Ensure we don't divide by zero
						if math.abs(touchRay.Direction.Y) > 0.001 then
							local touchWorldPos = touchRay.Origin + touchRay.Direction * ((currentGhostPosition.Y - touchRay.Origin.Y) / touchRay.Direction.Y)

							-- Calculate offset from ghost center to touch point
							local touchOffset = touchWorldPos - currentGhostPosition

							-- Start dragging
							isDraggingGhost = true
							dragStartPosition = touchPos
							dragStartWorldPosition = currentGhostPosition -- Store the current ghost world position
							dragTouchOffset = Vector3.new(touchOffset.X, 0, touchOffset.Z) -- Only use X,Z offset, keep Y at ghost level
							activeDragTouchPosition = touchPos -- Store the initial touch position
							print("Mobile: Started dragging ghost object at world position:", dragStartWorldPosition)
							print("Mobile: Touch offset from center:", dragTouchOffset)
						else
							print("Mobile: Cannot start drag - camera angle too horizontal")
						end

						-- Disable camera movement during drag
						disableCameraMovement()

						-- Setup drag tracking
						if dragConnection then
							dragConnection:Disconnect()
						end

						dragConnection = UserInputService.InputChanged:Connect(function(dragInput, dragProcessed)
							if dragInput.UserInputType == Enum.UserInputType.Touch and isDraggingGhost then
								local currentTouchPos = Vector2.new(dragInput.Position.X, dragInput.Position.Y)

								-- Check if this touch is in a reasonable area (not on movement controls)
								-- Movement controls are typically on the left side of screen
								local screenWidth = workspace.CurrentCamera.ViewportSize.X
								local isOnLeftSide = currentTouchPos.X < screenWidth * 0.3 -- Left 30% of screen

								if not isOnLeftSide then
									-- Calculate new position based on touch movement
									local validSurfaces = getValidPlacementSurfaces()
									if #validSurfaces > 0 then
										local raycastParams = RaycastParams.new()
										raycastParams.FilterType = Enum.RaycastFilterType.Include
										raycastParams.FilterDescendantsInstances = validSurfaces

										local camera = Workspace.CurrentCamera
										local unitRay = camera:ScreenPointToRay(currentTouchPos.X, currentTouchPos.Y)
										local raycastResult = Workspace:Raycast(unitRay.Origin, unitRay.Direction * 1000, raycastParams)

										if raycastResult then
											local hitNormal = raycastResult.Normal
											local upVector = Vector3.new(0, 1, 0)

											-- Determine surface type
											local isUpwardFacing = hitNormal:Dot(upVector) > 0.8
											local isDownwardFacing = hitNormal:Dot(upVector) < -0.8
											local isVertical = math.abs(hitNormal:Dot(upVector)) < 0.2

											if isUpwardFacing or isDownwardFacing or isVertical then
												local hitPosition = raycastResult.Position
												local placementPosition = calculateSidePlacement(hitPosition, hitNormal, ghostModel, raycastResult.Instance)

												if isUpwardFacing then
													local objectSize = getRotatedObjectSize(ghostModel)
													newFixedPosition = snapToGridWithSize(placementPosition, objectSize)
													isSidePlacement = false -- Top placement
												elseif isDownwardFacing then
													local objectSize = getRotatedObjectSize(ghostModel)
													newFixedPosition = snapToGridWithSize(placementPosition, objectSize)
													isSidePlacement = false -- Bottom placement
												else
													-- For side placement, check if we're placing against the plot or against another object
													local hitInstance = raycastResult.Instance
													local isPlacingAgainstPlot = false

													local playerPlot = getPlayerPlot()
													if playerPlot and hitInstance then
														isPlacingAgainstPlot = (hitInstance == playerPlot or hitInstance.Parent == playerPlot)
													end

													-- Apply plot boundary constraint to prevent object from going inside plot
													local constrainedPosition = constrainSidePlacementToPlotBounds(placementPosition, ghostModel, hitInstance)

													-- Size-aware grid snapping for mobile side placement
													local objectSize = getRotatedObjectSize(ghostModel)
													newFixedPosition = snapToGridWithSize(constrainedPosition, objectSize)
													isSidePlacement = true -- Side placement
													print("Mobile side placement - Size-aware grid snap:", newFixedPosition)
												end
											end

											-- For non-side placement, apply touch offset to maintain drag feel
											if not isSidePlacement then
												if dragStartWorldPosition and dragStartPosition and dragTouchOffset then
													-- Calculate where the current touch is in world space
													local currentRay = camera:ScreenPointToRay(currentTouchPos.X, currentTouchPos.Y)
													local planeY = newFixedPosition.Y
													if math.abs(currentRay.Direction.Y) > 0.001 then
														local currentTouchWorldPos = currentRay.Origin + currentRay.Direction * ((planeY - currentRay.Origin.Y) / currentRay.Direction.Y)
														-- Adjust the ghost position to maintain the touch offset
														local offsetPosition = currentTouchWorldPos - dragTouchOffset

														-- Apply grid snapping to the offset position
														local objectSize = getRotatedObjectSize(ghostModel)
														newFixedPosition = snapToGridWithSize(offsetPosition, objectSize)
														print("Mobile top/bottom placement - Applied grid snap to offset position:", newFixedPosition)
													end
												end
											end

											-- Use direction-preserving constraint instead of simple clamping
											newFixedPosition = constrainPositionToPlotBoundsWithDirection(newFixedPosition, mobileFixedPosition, ghostModel)
											-- Apply collision avoidance only for significant collisions (>0.2 studs on all axes)
											if checkGhostCollisionAtPosition(ghostModel, newFixedPosition) then
												newFixedPosition = findClosestValidPosition(ghostModel, newFixedPosition)
											end
											mobileFixedPosition = newFixedPosition
										else
											-- No valid surface hit - try to follow drag direction within plot bounds
											if dragStartWorldPosition and dragStartPosition and dragTouchOffset then
												-- Calculate world space position where current touch should place the ghost center
												local currentRay = camera:ScreenPointToRay(currentTouchPos.X, currentTouchPos.Y)

												-- Project current touch onto a horizontal plane at the ghost height
												local planeY = dragStartWorldPosition.Y

												-- Ensure we don't divide by zero
												if math.abs(currentRay.Direction.Y) > 0.001 then
													local currentTouchWorldPos = currentRay.Origin + currentRay.Direction * ((planeY - currentRay.Origin.Y) / currentRay.Direction.Y)

													-- Calculate where the ghost center should be based on the touch offset
													-- If user grabbed the corner, the center should be offset from the touch point
													local offsetPosition = currentTouchWorldPos - dragTouchOffset

													-- Apply grid snapping to the offset position
													local objectSize = getRotatedObjectSize(ghostModel)
													local newPosition = snapToGridWithSize(offsetPosition, objectSize)

													-- Constrain this projected position to plot bounds
													newPosition = constrainPositionToPlotBounds(newPosition, ghostModel)
													-- Apply collision avoidance only for significant collisions (>0.2 studs on all axes)
													if checkGhostCollisionAtPosition(ghostModel, newPosition) then
														newPosition = findClosestValidPosition(ghostModel, newPosition)
													end
													mobileFixedPosition = newPosition
													print("Mobile: Following drag with preserved touch offset and grid snap")
												else
													print("Mobile: Cannot calculate drag direction (camera angle too horizontal)")
												end
											end
										end
									end
								end
							end
						end)
					else
						print("Mobile: Touch not on ghost object, ignoring")
					end
				end
			end
		-- Handle PC mouse click input
		elseif not isMobileDevice() and input.UserInputType == Enum.UserInputType.MouseButton1 then
			print("PC mouse click detected")
			-- PC left click - only place if not clicking UI and enough time has passed since adjustment started
			print("PC: MouseButton1 detected, gameProcessed =", gameProcessed)
			if not gameProcessed then
				-- Check flag to prevent immediate placement after starting adjustment
				if not justStartedAdjustment then
					print("PC: Attempting to place object")
					placeObject()
				else
					print("PC: Ignoring click - adjustment just started")
				end
			else
				print("PC: Click was processed by UI, ignoring")
			end
		end

		-- Handle keyboard inputs
		if not gameProcessed then
			-- Only process keyboard inputs if not consumed by UI
			if input.KeyCode == Enum.KeyCode.Escape or input.KeyCode == Enum.KeyCode.Q then
				-- Escape or Q - cancel adjustment
				cancelAdjustment()
			elseif input.KeyCode == Enum.KeyCode.R then
				-- R key - rotate on Y-axis
				rotateObjectY()
			elseif input.KeyCode == Enum.KeyCode.T then
				-- T key - rotate on Z-axis
				rotateObjectZ()
			end
		end
	end)

	-- Connect input ending to stop dragging
	inputEndedConnection = UserInputService.InputEnded:Connect(function(input, gameProcessed)
		if isMobileDevice() and input.UserInputType == Enum.UserInputType.Touch and isDraggingGhost then
			-- Check if this touch end is not on the left side (movement controls)
			local endTouchPos = Vector2.new(input.Position.X, input.Position.Y)
			local screenWidth = workspace.CurrentCamera.ViewportSize.X
			local isOnLeftSide = endTouchPos.X < screenWidth * 0.3 -- Left 30% of screen

			-- If touch end is not on left side, it might be the drag ending
			if not isOnLeftSide then
				-- Stop dragging when any non-movement touch ends
				isDraggingGhost = false
				dragStartPosition = nil
				dragStartWorldPosition = nil
				dragTouchOffset = nil
				activeDragTouchPosition = nil

				-- Disconnect drag tracking
				if dragConnection then
					dragConnection:Disconnect()
					dragConnection = nil
				end

				-- Re-enable camera movement after drag
				enableCameraMovement()

				print("Mobile: Stopped dragging ghost object")
			end
		end
	end)

	-- Setup tool monitoring to cancel adjustment if tool is unequipped
	setupToolMonitoring()

	print("Adjustment mode started - using same placement system")
end

-- Create BindableEvent for adjust tool communication
local adjustToolEvent = ReplicatedStorage:FindFirstChild("AdjustToolEvent")
if not adjustToolEvent then
	adjustToolEvent = Instance.new("BindableEvent")
	adjustToolEvent.Name = "AdjustToolEvent"
	adjustToolEvent.Parent = ReplicatedStorage
end

-- Listen for adjust tool requests
adjustToolEvent.Event:Connect(function(objectToAdjust)
	if objectToAdjust and objectToAdjust:IsA("Model") then
		print("Placement script: Received adjust request for:", objectToAdjust.Name)

		-- Set flag to ignore touch events briefly
		adjustmentJustStarted = true
		task.spawn(function()
			task.wait(0.1) -- 100ms delay
			adjustmentJustStarted = false
		end)

		startAdjustment(objectToAdjust)
	end
end)

-- Cancel adjustment and restore original object
cancelAdjustment = function()
	if not isAdjusting then return end

	print("Cancelling adjustment")

	-- Restore original object to its original parent and position
	if originalObject and originalObjectPosition and originalObjectParent then
		-- Remove any ghost highlights from the original object before restoring it
		for _, child in pairs(originalObject:GetDescendants()) do
			if child:IsA("Highlight") then
				child:Destroy()
			end
		end

		-- Restore to original position
		if originalObject.PrimaryPart then
			originalObject:SetPrimaryPartCFrame(originalObjectPosition)
		end

		-- Restore original parent (move back to plot Objects folder)
		originalObject.Parent = originalObjectParent
		print("Restored object to original parent and position:", originalObject.Name)
	end

	-- Stop adjustment
	stopAdjustment()
end

-- Stop adjustment mode
stopAdjustment = function()
	isAdjusting = false
	originalObject = nil
	originalObjectPosition = nil
	originalObjectName = nil
	originalObjectParent = nil

	-- Reset mobile confirmation flag
	mobileAdjustmentConfirmed = false

	-- Clean up ghost
	if ghostModel then
		ghostModel:Destroy()
		ghostModel = nil
	end

	-- Disconnect connections
	if placementConnection then
		placementConnection:Disconnect()
		placementConnection = nil
	end

	if inputConnection then
		inputConnection:Disconnect()
		inputConnection = nil
	end

	if inputEndedConnection then
		inputEndedConnection:Disconnect()
		inputEndedConnection = nil
	end

	-- Reset position state
	resetPosition()

	-- Hide control UI and cleanup mobile buttons
	hideControlUI()
	if isMobileDevice() then
		cleanupMobileButtons()
	end

	-- Re-enable camera movement if it was disabled
	enableCameraMovement()

	-- Reset placement state
	isPlacing = false

	-- Notify AdjustTool that adjustment has ended
	local adjustmentEndEvent = ReplicatedStorage:FindFirstChild("AdjustmentEndEvent")
	if adjustmentEndEvent then
		adjustmentEndEvent:Fire()
	end

	print("Adjustment mode stopped")
end

-- Build button functionality moved to BuildMenu.client.lua

-- ========================================
-- ADJUST TOOL FUNCTIONS
-- ========================================

local function setupAdjustToolMonitoring()
	print("Setting up Adjust tool monitoring...")

	-- Create button press event for mobile
	local buttonPressEvent = ReplicatedStorage:FindFirstChild("MobileButtonPressEvent")
	if not buttonPressEvent then
		buttonPressEvent = Instance.new("BindableEvent")
		buttonPressEvent.Name = "MobileButtonPressEvent"
		buttonPressEvent.Parent = ReplicatedStorage
	end

	buttonPressEvent.Event:Connect(function()
		lastButtonPressTime = tick()
		print("Adjust tool: Mobile button press detected, updating cooldown time")
	end)

	-- Listen for mobile mode changes to clear tap states
	local modeChangeEvent = ReplicatedStorage:FindFirstChild("MobileModeChangeEvent")
	if not modeChangeEvent then
		modeChangeEvent = Instance.new("BindableEvent")
		modeChangeEvent.Name = "MobileModeChangeEvent"
		modeChangeEvent.Parent = ReplicatedStorage
	end

	modeChangeEvent.Event:Connect(function(newMode)
		print("Adjust tool: Mobile mode changed to:", newMode, "- clearing tap states")
		clearMobileTapStates()
	end)

	-- Listen for adjustment end notifications from placement script
	local adjustmentEndEvent = ReplicatedStorage:FindFirstChild("AdjustmentEndEvent")
	if not adjustmentEndEvent then
		adjustmentEndEvent = Instance.new("BindableEvent")
		adjustmentEndEvent.Name = "AdjustmentEndEvent"
		adjustmentEndEvent.Parent = ReplicatedStorage
	end

	adjustmentEndEvent.Event:Connect(function()
		print("Adjust tool: Received adjustment end notification")
		isAdjusting = false
	end)

	local function monitorCharacter(character)
		if not character then
			print("No character provided to monitorCharacter")
			return
		end

		print("Monitoring character:", character.Name)

		-- Monitor for Adjust tool being equipped
		character.ChildAdded:Connect(function(child)
			print("Tool added to character:", child.Name, child.ClassName)
			-- Debug: Print all tool properties
			if child:IsA("Tool") then
				print("Tool details - Name:", child.Name, "ClassName:", child.ClassName, "Parent:", child.Parent and child.Parent.Name or "nil")
			end

			if child.Name == "Adjust" and child:IsA("Tool") then
				print("Adjust tool equipped - starting hover highlighting")
				adjustToolEquipped = true

				-- Fade in AdjustFrame UI (PC) and MobileAdjustFrame UI (Mobile)
				local fadeInEvent = ReplicatedStorage:FindFirstChild("FadeInAdjustFrameEvent")
				if not fadeInEvent then
					fadeInEvent = Instance.new("BindableEvent")
					fadeInEvent.Name = "FadeInAdjustFrameEvent"
					fadeInEvent.Parent = ReplicatedStorage
				end
				fadeInEvent:Fire()

				local fadeInMobileEvent = ReplicatedStorage:FindFirstChild("FadeInMobileAdjustFrameEvent")
				if not fadeInMobileEvent then
					fadeInMobileEvent = Instance.new("BindableEvent")
					fadeInMobileEvent.Name = "FadeInMobileAdjustFrameEvent"
					fadeInMobileEvent.Parent = ReplicatedStorage
				end
				fadeInMobileEvent:Fire()

				-- Start hover highlighting
				if adjustHoverConnection then
					adjustHoverConnection:Disconnect()
				end
				adjustHoverConnection = RunService.Heartbeat:Connect(handleAdjustHover)

				-- Connect tool activation
				adjustToolActivatedConnection = child.Activated:Connect(onAdjustToolActivated)

				-- Connect input monitoring
				if adjustInputConnection then
					adjustInputConnection:Disconnect()
				end
				adjustInputConnection = UserInputService.InputBegan:Connect(onAdjustInput)

				print("Adjust tool equipped and monitoring started")


			end
		end)

		-- Monitor for Adjust tool being unequipped
		character.ChildRemoved:Connect(function(child)
			if child.Name == "Adjust" and child:IsA("Tool") then
				print("Adjust tool unequipped - stopping hover highlighting")
				adjustToolEquipped = false

				-- Clear mobile tap states when tool is unequipped
				clearMobileTapStates()

				-- Fade out AdjustFrame UI (PC) and MobileAdjustFrame UI (Mobile)
				local fadeOutEvent = ReplicatedStorage:FindFirstChild("FadeOutAdjustFrameEvent")
				if not fadeOutEvent then
					fadeOutEvent = Instance.new("BindableEvent")
					fadeOutEvent.Name = "FadeOutAdjustFrameEvent"
					fadeOutEvent.Parent = ReplicatedStorage
				end
				fadeOutEvent:Fire()

				local fadeOutMobileEvent = ReplicatedStorage:FindFirstChild("FadeOutMobileAdjustFrameEvent")
				if not fadeOutMobileEvent then
					fadeOutMobileEvent = Instance.new("BindableEvent")
					fadeOutMobileEvent.Name = "FadeOutMobileAdjustFrameEvent"
					fadeOutMobileEvent.Parent = ReplicatedStorage
				end
				fadeOutMobileEvent:Fire()

				-- Clean up connections
				if adjustHoverConnection then
					adjustHoverConnection:Disconnect()
					adjustHoverConnection = nil
				end

				if adjustInputConnection then
					adjustInputConnection:Disconnect()
					adjustInputConnection = nil
				end

				-- Clean up tool activation connection
				if adjustToolActivatedConnection then
					adjustToolActivatedConnection:Disconnect()
					adjustToolActivatedConnection = nil
				end

				-- Clear highlights
				if adjustHoveredObject then
					removeAdjustHighlight(adjustHoveredObject)
					adjustHoveredObject = nil
				end

				if adjustSelectedObject then
					removeAdjustHighlight(adjustSelectedObject)
					adjustSelectedObject = nil
					adjustSelectedMode = nil
				end

				print("Adjust tool monitoring stopped")

				-- If we're currently adjusting, cancel it
				if isAdjusting then
					print("Adjust tool unequipped while adjusting - cancelling adjustment")
					cancelAdjustment()
				end
			end
		end)
	end

	-- Monitor current character
	if player.Character then
		print("Found existing character:", player.Character.Name)
		monitorCharacter(player.Character)

		-- Also check if Adjust tool is already equipped
		local adjustTool = player.Character:FindFirstChild("Adjust")
		if adjustTool then
			print("Adjust tool already equipped on character load")
		end
	else
		print("No character found on script load")
	end

	-- Monitor future characters
	player.CharacterAdded:Connect(function(character)
		print("New character added:", character.Name)
		monitorCharacter(character)
	end)
end

-- Handle server response for object adjustment
local adjustResponseEvent = ReplicatedStorage:WaitForChild("Events"):WaitForChild("AdjustObjectResponse")
adjustResponseEvent.OnClientEvent:Connect(function(success, message)
	if not success then
		warn("Object adjustment failed:", message)
		showPlacementError("The object could not be moved: " .. (message or "Unknown error"))
		-- If adjustment failed, restore object to original parent and position
		if originalObject and originalObjectPosition and originalObjectParent then
			print("Reverting object to original position due to server rejection")

			-- Remove any ghost highlights from the original object before restoring it
			for _, child in pairs(originalObject:GetDescendants()) do
				if child:IsA("Highlight") then
					child:Destroy()
				end
			end

			-- Restore original position
			if originalObject.PrimaryPart then
				originalObject:SetPrimaryPartCFrame(originalObjectPosition)
			end

			-- Restore original parent (move back to plot Objects folder)
			originalObject.Parent = originalObjectParent
			print("Restored object to original parent and position:", originalObject.Name)
		end

		-- Stop placement and adjustment mode
		stopPlacement()
		stopAdjustment()
	else
		print("Object adjustment confirmed by server:", message)
		-- Server confirmed the adjustment - the server has already moved the object to the new position
		-- We just need to move the object back from ReplicatedStorage to the plot folder
		-- The server's position will replicate automatically

		if originalObject and originalObjectParent then
			-- Remove any ghost highlights from the original object before moving it back
			for _, child in pairs(originalObject:GetDescendants()) do
				if child:IsA("Highlight") then
					child:Destroy()
				end
			end

			-- Move the object back to the plot Objects folder
			-- Don't set position - let the server's position replicate naturally
			originalObject.Parent = originalObjectParent
			print("Moved adjusted object back to plot - server position will replicate:", originalObject.Name)

			-- Play placement sound at the object's new position with pitch based on object size
			local objectPosition = originalObject.PrimaryPart and originalObject.PrimaryPart.Position or originalObject:GetModelCFrame().Position
			playPlacementSound(objectPosition, originalObject)
			print("Adjustment successful - object placed at new position")
		end

		-- Stop placement and adjustment mode (this will clean up the ghost)
		stopPlacement()
		stopAdjustment()
	end
end)

-- ========================================
-- INITIALIZATION
-- ========================================

print("Placement script: Initializing...")

-- Wrap initialization in pcall to catch any errors
local success, errorMessage = pcall(function()
	setupAdjustToolMonitoring()
end)

if success then
	print("Placement script: Initialization complete")
else
	warn("Placement script: Initialization failed:", errorMessage)
end

-- Ensure StartPlacement is available even if initialization fails
print("Placement script: StartPlacement function available:", _G.StartPlacement ~= nil)

