local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Workspace = game:GetService("Workspace")
local Players = game:GetService("Players")

-- Constants
local MAX_TOOLS = 8 -- Maximum number of tools a player can hold

-- Function to count total tools a player has
local function countPlayerTools(player)
    local backpack = player:FindFirstChild("Backpack")
    local character = player.Character
    local toolCount = 0

    -- Count tools in backpack
    if backpack then
        for _, child in pairs(backpack:GetChildren()) do
            if child:IsA("Tool") then
                toolCount = toolCount + 1
            end
        end
    end

    -- Count tools equipped on character
    if character then
        for _, child in pairs(character:<PERSON><PERSON><PERSON>dre<PERSON>()) do
            if child:Is<PERSON>("Tool") then
                toolCount = toolCount + 1
            end
        end
    end

    return toolCount
end

-- Function to give a tool to player with limit checking
local function giveToolToPlayer(player, toolName, toolObject)
    local backpack = player:FindFirstChild("Backpack")
    local character = player.Character

    if not backpack then
        warn("Player", player.Name, "has no backpack")
        return false
    end

    -- Check if player already has this specific tool
    if backpack:FindFirstChild(toolName) or (character and character:FindFirstChild(toolName)) then
        print("Player", player.Name, "already has", toolName)
        return false
    end

    -- Check tool limit
    local currentToolCount = countPlayerTools(player)
    if currentToolCount >= MAX_TOOLS then
        warn("Player", player.Name, "has reached the maximum tool limit of", MAX_TOOLS)
        return false
    end

    -- Give the tool
    local toolClone = toolObject:Clone()
    toolClone.Parent = backpack
    print("Gave", toolName, "to", player.Name, "'s backpack (", currentToolCount + 1, "/", MAX_TOOLS, ")")
    return true
end

-- Function to give tools to player (Blueprint and Adjust tools)
local function givePlayerTools(player)
    local toolsFolder = ReplicatedStorage:FindFirstChild("Tools")
    if not toolsFolder then
        warn("Tools folder not found in ReplicatedStorage")
        return
    end

    local backpack = player:FindFirstChild("Backpack")
    local character = player.Character

    if not backpack then
        warn("Player", player.Name, "has no backpack")
        return
    end

    -- Give Blueprint tool
    local blueprintTool = toolsFolder:FindFirstChild("Blueprints")
    if blueprintTool then
        giveToolToPlayer(player, "Blueprints", blueprintTool)
    else
        warn("Blueprint tool not found in ReplicatedStorage.Tools.Blueprints")
    end

    -- Give Adjust tool
    local adjustTool = toolsFolder:FindFirstChild("Adjust")
    if adjustTool then
        giveToolToPlayer(player, "Adjust", adjustTool)
    else
        warn("Adjust tool not found in ReplicatedStorage.Tools.Adjust")
    end
end

-- Handle PlotSelectionEvent (existing functionality)
local plotSelectionEvent = ReplicatedStorage:WaitForChild("Events"):WaitForChild("PlotSelectionEvent")

plotSelectionEvent.OnServerEvent:Connect(function(player, plotName)
    local plots = workspace:FindFirstChild("Plots")
    if not plots then return end
    -- Check if player already owns a plot
    for _, plotFolder in ipairs(plots:GetChildren()) do
        local values = plotFolder:FindFirstChild("Values")
        local ownerId = values and values:FindFirstChild("OwnerId")
        if ownerId and ownerId.Value == player.UserId then
            return -- Player already owns a plot, do not assign another
        end
    end
    local plotFolder = plots:FindFirstChild(plotName)
    if plotFolder and plotFolder:FindFirstChild("Values") and plotFolder.Values:FindFirstChild("OwnerId") then
        local ownerId = plotFolder.Values.OwnerId
        if ownerId.Value == 0 or ownerId.Value == nil then -- Only allow if unowned
            ownerId.Value = player.UserId

            -- Give tools to player after successful plot selection
            givePlayerTools(player)

            -- Setup respawn handling to re-give tools on death
            local connection
            connection = player.CharacterAdded:Connect(function(character)
                -- Wait a moment for character to fully load
                wait(0.5)
                givePlayerTools(player)
            end)
        end
    end
end)

-- Get player's plot folder
local function getPlayerPlotFolder(player)
	local plots = Workspace:FindFirstChild("Plots")
	if not plots then return nil end
	
	-- Find player's plot folder
	for _, plotFolder in pairs(plots:GetChildren()) do
		local values = plotFolder:FindFirstChild("Values")
		local ownerId = values and values:FindFirstChild("OwnerId")
		
		if ownerId and ownerId.Value == player.UserId then
			return plotFolder
		end
	end
	
	return nil
end

-- Check if position is on or adjacent to player's plot (server-side validation)
-- This allows both normal placement (on plot) and edge placement (adjacent to plot)
local function isPositionOnOrAdjacentToPlayerPlot(player, position)
	local plots = Workspace:FindFirstChild("Plots")
	if not plots then return false end

	-- Find player's plot
	for _, plotFolder in pairs(plots:GetChildren()) do
		local values = plotFolder:FindFirstChild("Values")
		local ownerId = values and values:FindFirstChild("OwnerId")

		if ownerId and ownerId.Value == player.UserId then
			-- Found player's plot, check if position is within it or adjacent to it
			local plot = plotFolder:FindFirstChild("Plot")
			if plot and plot:IsA("BasePart") then
				local plotPos = plot.Position
				local plotSize = plot.Size

				-- Check if position is within plot bounds (X and Z)
				local minX = plotPos.X - plotSize.X/2
				local maxX = plotPos.X + plotSize.X/2
				local minZ = plotPos.Z - plotSize.Z/2
				local maxZ = plotPos.Z + plotSize.Z/2

				-- Check if position is above the plot (Y coordinate)
				local plotTopY = plotPos.Y + plotSize.Y/2

				-- Allow placement if position is within plot bounds
				if position.X >= minX and position.X <= maxX and
				   position.Z >= minZ and position.Z <= maxZ and
				   position.Y >= plotTopY then
					print("Server: Allowing normal placement for", player.Name, "at", position)
					return true
				end

				-- Also allow placement if position is adjacent to plot (edge placement)
				-- Define adjacency tolerance (how far outside plot bounds we allow)
				local adjacencyTolerance = 25 -- studs outside plot bounds (increased for edge placement)
				local extendedMinX = minX - adjacencyTolerance
				local extendedMaxX = maxX + adjacencyTolerance
				local extendedMinZ = minZ - adjacencyTolerance
				local extendedMaxZ = maxZ + adjacencyTolerance

				-- Check if position is in extended area and above plot level
				if position.X >= extendedMinX and position.X <= extendedMaxX and
				   position.Z >= extendedMinZ and position.Z <= extendedMaxZ and
				   position.Y >= plotTopY then
					print("Server: Allowing edge placement for", player.Name, "at", position)
					return true
				end

				-- Debug: Log why placement was rejected
				print("Server: Rejecting placement for", player.Name, "at", position)
				print("  Plot bounds: X[", minX, ",", maxX, "] Z[", minZ, ",", maxZ, "] Y>=", plotTopY)
				print("  Extended bounds: X[", extendedMinX, ",", extendedMaxX, "] Z[", extendedMinZ, ",", extendedMaxZ, "]")
				print("  Position check: X=", position.X, "Z=", position.Z, "Y=", position.Y)
			end
			break -- Found player's plot, no need to check others
		end
	end

	return false
end

-- Get rotated bounding box corners for a part (server-side)
local function getRotatedBoundingBox(part)
	local cf = part.CFrame
	local size = part.Size
	local corners = {}
	
	-- Calculate all 8 corners of the bounding box
	local halfSize = size / 2
	local offsets = {
		Vector3.new(-halfSize.X, -halfSize.Y, -halfSize.Z),
		Vector3.new( halfSize.X, -halfSize.Y, -halfSize.Z),
		Vector3.new(-halfSize.X,  halfSize.Y, -halfSize.Z),
		Vector3.new( halfSize.X,  halfSize.Y, -halfSize.Z),
		Vector3.new(-halfSize.X, -halfSize.Y,  halfSize.Z),
		Vector3.new( halfSize.X, -halfSize.Y,  halfSize.Z),
		Vector3.new(-halfSize.X,  halfSize.Y,  halfSize.Z),
		Vector3.new( halfSize.X,  halfSize.Y,  halfSize.Z)
	}
	
	for i, offset in ipairs(offsets) do
		corners[i] = cf:PointToWorldSpace(offset)
	end
	
	return corners
end

-- Get axis-aligned bounding box from rotated corners (server-side)
local function getAABBFromCorners(corners)
	local minX, maxX = corners[1].X, corners[1].X
	local minY, maxY = corners[1].Y, corners[1].Y
	local minZ, maxZ = corners[1].Z, corners[1].Z
	
	for _, corner in ipairs(corners) do
		minX = math.min(minX, corner.X)
		maxX = math.max(maxX, corner.X)
		minY = math.min(minY, corner.Y)
		maxY = math.max(maxY, corner.Y)
		minZ = math.min(minZ, corner.Z)
		maxZ = math.max(maxZ, corner.Z)
	end
	
	return {
		min = Vector3.new(minX, minY, minZ),
		max = Vector3.new(maxX, maxY, maxZ)
	}
end

-- Check if a new object would collide with existing objects in the player's plot (rotation-aware)
local function checkObjectCollision(player, newObject, position)
	-- Get player's plot folder
	local playerPlotFolder = getPlayerPlotFolder(player)
	if not playerPlotFolder then return false end

	-- Get the Objects folder within the player's plot
	local objectsFolder = playerPlotFolder:FindFirstChild("Objects")
	if not objectsFolder then return false end -- No objects placed yet, no collision

	-- Check if new object has PrimaryPart (hitbox)
	if not newObject.PrimaryPart then
		warn("Object", newObject.Name, "has no PrimaryPart - cannot check collision")
		return false -- Allow placement if no hitbox defined
	end

	-- Get new object's rotated bounding box
	local newCorners = getRotatedBoundingBox(newObject.PrimaryPart)
	local newAABB = getAABBFromCorners(newCorners)
	
	-- Check collision with each existing object's hitbox
	for _, existingObject in pairs(objectsFolder:GetChildren()) do
		if existingObject:IsA("Model") and existingObject.PrimaryPart then
			-- Get existing object's rotated bounding box
			local existingCorners = getRotatedBoundingBox(existingObject.PrimaryPart)
			local existingAABB = getAABBFromCorners(existingCorners)

			-- New collision system: Allow 0.2 stud penetration tolerance and complete side-by-side freedom
			local penetrationTolerance = 0.2

			-- Check if bounding boxes overlap on all axes
			local overlapsX = newAABB.max.X > existingAABB.min.X and newAABB.min.X < existingAABB.max.X
			local overlapsY = newAABB.max.Y > existingAABB.min.Y and newAABB.min.Y < existingAABB.max.Y
			local overlapsZ = newAABB.max.Z > existingAABB.min.Z and newAABB.min.Z < existingAABB.max.Z

			-- Only check penetration if overlapping on all three axes
			if overlapsX and overlapsY and overlapsZ then
				-- Calculate how much the new object penetrates into the existing object
				local penetrationX = math.min(newAABB.max.X - existingAABB.min.X, existingAABB.max.X - newAABB.min.X)
				local penetrationY = math.min(newAABB.max.Y - existingAABB.min.Y, existingAABB.max.Y - newAABB.min.Y)
				local penetrationZ = math.min(newAABB.max.Z - existingAABB.min.Z, existingAABB.max.Z - newAABB.min.Z)

				-- Allow touching placements: if any axis has minimal penetration (≤0.3 studs), allow it
				-- This ensures side-by-side and edge touching placements work with more forgiving tolerance
				local touchingTolerance = 0.3 -- Increased from 0.05 to make placement less sensitive
				if penetrationX <= touchingTolerance or penetrationY <= touchingTolerance or penetrationZ <= touchingTolerance then
					continue -- Allow touching/minimal overlap placements
				end

				-- Prevent collision if there's significant penetration on all axes (>0.3 studs)
				-- This prevents objects from being placed inside each other while allowing more forgiving placement
				if penetrationX > touchingTolerance and penetrationY > touchingTolerance and penetrationZ > touchingTolerance then
					return true -- Significant penetration detected - objects would be inside each other
				end
			end
		end
	end
	
	return false -- No collision
end

-- Get rotated bounding box corners from CFrame and Size (server-side)
local function getBoundingBoxCorners(cframe, size)
	local corners = {}

	-- Calculate all 8 corners of the bounding box
	local halfSize = size / 2
	local offsets = {
		Vector3.new(-halfSize.X, -halfSize.Y, -halfSize.Z),
		Vector3.new( halfSize.X, -halfSize.Y, -halfSize.Z),
		Vector3.new(-halfSize.X,  halfSize.Y, -halfSize.Z),
		Vector3.new( halfSize.X,  halfSize.Y, -halfSize.Z),
		Vector3.new(-halfSize.X, -halfSize.Y,  halfSize.Z),
		Vector3.new( halfSize.X, -halfSize.Y,  halfSize.Z),
		Vector3.new(-halfSize.X,  halfSize.Y,  halfSize.Z),
		Vector3.new( halfSize.X,  halfSize.Y,  halfSize.Z)
	}

	for i, offset in ipairs(offsets) do
		corners[i] = cframe:PointToWorldSpace(offset)
	end

	return corners
end

-- Check if any players would be inside the object's placement area
local function arePlayersInPlacementArea(newObject, position)
	if not newObject or not newObject.PrimaryPart then
		return false -- If no hitbox, allow placement
	end

	local Players = game:GetService("Players")

	-- Calculate the object's rotated bounding box at the NEW position (not current position)
	-- Use the object's current rotation but at the new position
	local currentRotation = newObject.PrimaryPart.CFrame - newObject.PrimaryPart.Position
	local targetCFrame = CFrame.new(position) * currentRotation
	local hitboxSize = newObject.PrimaryPart.Size
	local hitboxCorners = getBoundingBoxCorners(targetCFrame, hitboxSize)
	local hitboxAABB = getAABBFromCorners(hitboxCorners)

	-- Add a buffer to prevent players from getting stuck
	local buffer = 2.0 -- Larger buffer to ensure safe placement
	hitboxAABB.min = hitboxAABB.min - Vector3.new(buffer, buffer, buffer)
	hitboxAABB.max = hitboxAABB.max + Vector3.new(buffer, buffer, buffer)

	-- Check all players
	for _, player in pairs(Players:GetPlayers()) do
		if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
			local playerPosition = player.Character.HumanoidRootPart.Position

			-- Check if player is within the placement area
			if playerPosition.X >= hitboxAABB.min.X and playerPosition.X <= hitboxAABB.max.X and
			   playerPosition.Y >= hitboxAABB.min.Y and playerPosition.Y <= hitboxAABB.max.Y and
			   playerPosition.Z >= hitboxAABB.min.Z and playerPosition.Z <= hitboxAABB.max.Z then
				return true, player.Name -- Return true and player name
			end
		end
	end

	return false
end

-- Handle PlaceObject event (new placement functionality)
local placeObjectEvent = ReplicatedStorage:WaitForChild("Events"):WaitForChild("PlaceObject")

placeObjectEvent.OnServerEvent:Connect(function(player, objectName, cframe)
	local position = cframe.Position
	print("Player", player.Name, "wants to place", objectName, "at", position, "with rotation")
	
	-- Check if player has this object in inventory (server-side validation)
	if _G.ObjectInventory and _G.ObjectInventory.canPlaceObject then
		if not _G.ObjectInventory.canPlaceObject(player, objectName) then
			warn("Player", player.Name, "attempted to place", objectName, "but doesn't have any in inventory!")
			return
		end
	else
		warn("ObjectInventory system not loaded yet!")
		return
	end
	
	-- First, validate that the position is on or adjacent to the player's plot
	if not isPositionOnOrAdjacentToPlayerPlot(player, position) then
		warn("Player", player.Name, "attempted to place object outside their plot area!")
		return
	end
	
	-- Get the object from ReplicatedStorage/Objects
	local objectsFolder = ReplicatedStorage:FindFirstChild("Objects")
	if not objectsFolder then
		warn("Objects folder not found in ReplicatedStorage")
		return
	end
	
	local objectToPlace = objectsFolder:FindFirstChild(objectName)
	if not objectToPlace then
		warn("Object '" .. objectName .. "' not found in Objects folder")
		return
	end
	
	if not objectToPlace:IsA("Model") then
		warn("Object '" .. objectName .. "' is not a Model")
		return
	end
	
	-- Clone the object
	local newObject = objectToPlace:Clone()
	newObject.Name = objectName .. "_" .. tick() -- Add timestamp to make unique

	-- Position and rotate the object FIRST before checking collisions
	if newObject.PrimaryPart then
		newObject:SetPrimaryPartCFrame(cframe)
	else
		-- If no PrimaryPart, we need to move the entire model properly
		-- Calculate the model's current center
		local modelCFrame = newObject:GetModelCFrame()
		local offset = cframe.Position - modelCFrame.Position

		-- Move all parts by the offset and apply rotation
		for _, part in pairs(newObject:GetDescendants()) do
			if part:IsA("BasePart") then
				local relativeCFrame = part.CFrame - modelCFrame.Position
				part.CFrame = cframe * relativeCFrame
			end
		end
	end

	-- Check for collisions after the object is properly positioned
	local hasCollision = checkObjectCollision(player, newObject, position)
	if hasCollision then
		warn("Player", player.Name, "attempted to place object that would overlap with existing objects!")
		newObject:Destroy() -- Clean up the cloned object
		return
	end
	
	-- Check if any players would be in the placement area
	local playersInArea, playerName = arePlayersInPlacementArea(newObject, position)
	if playersInArea then
		warn("Player", player.Name, "attempted to place object where", playerName, "is standing!")
		newObject:Destroy() -- Clean up the cloned object
		return
	end
	
	-- Make sure all parts are properly configured
	for _, part in pairs(newObject:GetDescendants()) do
		if part:IsA("BasePart") then
			part.Anchored = true -- Keep objects anchored after placement
			-- Set collision based on whether it's the hitbox or not
			if part == newObject.PrimaryPart then
				part.CanCollide = false -- Hitbox should never have collision
			else
				part.CanCollide = true -- Other parts can have collision
			end
		end
	end
	
	-- Get player's plot folder
	local playerPlotFolder = getPlayerPlotFolder(player)
	if not playerPlotFolder then
		warn("Could not find plot folder for player", player.Name)
		return
	end
	
	-- Create or get the Objects folder within the player's plot
	local objectsFolder = playerPlotFolder:FindFirstChild("Objects")
	if not objectsFolder then
		objectsFolder = Instance.new("Folder")
		objectsFolder.Name = "Objects"
		objectsFolder.Parent = playerPlotFolder
	end
	
	-- Parent to the Objects folder in the player's plot
	newObject.Parent = objectsFolder

	-- Use one object from inventory (decrease count by 1)
	if _G.ObjectInventory and _G.ObjectInventory.useObject then
		local success = _G.ObjectInventory.useObject(player, objectName)
		if success then
			print("Successfully placed", objectName, "at", position, "with rotation in", player.Name .. "'s plot Objects folder")
			print("Player", player.Name, "now has", _G.ObjectInventory.getObjectCount(player, objectName), objectName .. "(s) remaining")

			-- Save plot objects after placing
			if _G.PlotObjectSaver then
				task.spawn(function()
					_G.PlotObjectSaver.savePlayerObjects(player)
				end)
			end
		else
			warn("Failed to use object from inventory for", player.Name)
		end
	else
		print("Successfully placed", objectName, "at", position, "with rotation in", player.Name .. "'s plot Objects folder")

		-- Save plot objects after placing
		if _G.PlotObjectSaver then
			task.spawn(function()
				_G.PlotObjectSaver.savePlayerObjects(player)
			end)
		end
	end
end)

-- Handle AdjustObject event (moving existing objects)
local adjustObjectEvent = ReplicatedStorage:WaitForChild("Events"):WaitForChild("AdjustObject")

adjustObjectEvent.OnServerEvent:Connect(function(player, objectName, newCFrame)
	local position = newCFrame.Position
	print("Player", player.Name, "wants to adjust", objectName, "to", position, "with rotation")

	-- First, validate that the new position is on or adjacent to the player's plot
	if not isPositionOnOrAdjacentToPlayerPlot(player, position) then
		warn("Player", player.Name, "attempted to move object outside their plot area!")
		-- Send failure response to client
		local adjustResponseEvent = ReplicatedStorage:WaitForChild("Events"):WaitForChild("AdjustObjectResponse")
		adjustResponseEvent:FireClient(player, false, "Object must be placed within your plot area")
		return
	end

	-- Get player's plot folder
	local playerPlotFolder = getPlayerPlotFolder(player)
	if not playerPlotFolder then
		warn("Could not find plot folder for player", player.Name)
		-- Send failure response to client
		local adjustResponseEvent = ReplicatedStorage:WaitForChild("Events"):WaitForChild("AdjustObjectResponse")
		adjustResponseEvent:FireClient(player, false, "Could not find your plot")
		return
	end

	-- Find the Objects folder within the player's plot
	local objectsFolder = playerPlotFolder:FindFirstChild("Objects")
	if not objectsFolder then
		warn("No Objects folder found in", player.Name .. "'s plot")
		-- Send failure response to client
		local adjustResponseEvent = ReplicatedStorage:WaitForChild("Events"):WaitForChild("AdjustObjectResponse")
		adjustResponseEvent:FireClient(player, false, "No objects folder found")
		return
	end

	-- Find the specific object to adjust
	local objectToAdjust = objectsFolder:FindFirstChild(objectName)
	if not objectToAdjust then
		warn("Object", objectName, "not found in", player.Name .. "'s plot")
		-- Send failure response to client
		local adjustResponseEvent = ReplicatedStorage:WaitForChild("Events"):WaitForChild("AdjustObjectResponse")
		adjustResponseEvent:FireClient(player, false, "Object not found in your plot")
		return
	end

	if not objectToAdjust:IsA("Model") then
		warn("Object", objectName, "is not a Model")
		-- Send failure response to client
		local adjustResponseEvent = ReplicatedStorage:WaitForChild("Events"):WaitForChild("AdjustObjectResponse")
		adjustResponseEvent:FireClient(player, false, "Invalid object type")
		return
	end

	-- Create a temporary clone to test collision at new position
	local testObject = objectToAdjust:Clone()

	-- Position the test object at the new location
	if testObject.PrimaryPart then
		testObject:SetPrimaryPartCFrame(newCFrame)
	else
		-- If no PrimaryPart, move the entire model properly
		local modelCFrame = testObject:GetModelCFrame()
		local offset = newCFrame.Position - modelCFrame.Position

		for _, part in pairs(testObject:GetDescendants()) do
			if part:IsA("BasePart") then
				local relativeCFrame = part.CFrame - modelCFrame.Position
				part.CFrame = newCFrame * relativeCFrame
			end
		end
	end

	-- Check for collisions with other objects (excluding the object being moved)
	local hasCollision = false
	for _, existingObject in pairs(objectsFolder:GetChildren()) do
		if existingObject ~= objectToAdjust and existingObject:IsA("Model") and existingObject.PrimaryPart then
			-- Check collision between test object and existing objects
			if testObject.PrimaryPart then
				local testCorners = getRotatedBoundingBox(testObject.PrimaryPart)
				local testAABB = getAABBFromCorners(testCorners)

				local existingCorners = getRotatedBoundingBox(existingObject.PrimaryPart)
				local existingAABB = getAABBFromCorners(existingCorners)

				-- Check if bounding boxes overlap on all axes
				local overlapsX = testAABB.max.X > existingAABB.min.X and testAABB.min.X < existingAABB.max.X
				local overlapsY = testAABB.max.Y > existingAABB.min.Y and testAABB.min.Y < existingAABB.max.Y
				local overlapsZ = testAABB.max.Z > existingAABB.min.Z and testAABB.min.Z < existingAABB.max.Z

				-- Only check penetration if overlapping on all three axes
				if overlapsX and overlapsY and overlapsZ then
					-- Calculate how much the moved object penetrates into the existing object
					local penetrationX = math.min(testAABB.max.X - existingAABB.min.X, existingAABB.max.X - testAABB.min.X)
					local penetrationY = math.min(testAABB.max.Y - existingAABB.min.Y, existingAABB.max.Y - testAABB.min.Y)
					local penetrationZ = math.min(testAABB.max.Z - existingAABB.min.Z, existingAABB.max.Z - testAABB.min.Z)

					-- Allow touching placements: if any axis has minimal penetration (≤0.3 studs), allow it
					local touchingTolerance = 0.3 -- Increased from 0.05 to make placement less sensitive
					if penetrationX <= touchingTolerance or penetrationY <= touchingTolerance or penetrationZ <= touchingTolerance then
						continue -- Allow touching/minimal overlap placements
					end

					-- Prevent collision if there's significant penetration on all axes (>0.3 studs)
					-- This prevents objects from being placed inside each other while allowing more forgiving placement
					if penetrationX > touchingTolerance and penetrationY > touchingTolerance and penetrationZ > touchingTolerance then
						hasCollision = true -- Significant penetration detected - objects would be inside each other
						break
					end
				end
			end
		end
	end

	-- Clean up test object
	testObject:Destroy()

	if hasCollision then
		warn("Player", player.Name, "attempted to move object to a position that would overlap with existing objects!")
		-- Send failure response to client
		local adjustResponseEvent = ReplicatedStorage:WaitForChild("Events"):WaitForChild("AdjustObjectResponse")
		adjustResponseEvent:FireClient(player, false, "Object would overlap with existing objects")
		return
	end

	-- Check if any players would be in the new placement area
	local playersInArea, playerName = arePlayersInPlacementArea(objectToAdjust, position)
	if playersInArea then
		warn("Player", player.Name, "attempted to move object where", playerName, "is standing!")
		-- Send failure response to client
		local adjustResponseEvent = ReplicatedStorage:WaitForChild("Events"):WaitForChild("AdjustObjectResponse")
		adjustResponseEvent:FireClient(player, false, "Player " .. playerName .. " is in the way")
		return
	end

	-- All checks passed, move the object to the new position
	if objectToAdjust.PrimaryPart then
		objectToAdjust:SetPrimaryPartCFrame(newCFrame)
	else
		-- If no PrimaryPart, move the entire model properly
		local modelCFrame = objectToAdjust:GetModelCFrame()
		local offset = newCFrame.Position - modelCFrame.Position

		for _, part in pairs(objectToAdjust:GetDescendants()) do
			if part:IsA("BasePart") then
				local relativeCFrame = part.CFrame - modelCFrame.Position
				part.CFrame = newCFrame * relativeCFrame
			end
		end
	end

	print("Successfully adjusted", objectName, "to", position, "with rotation for", player.Name)

	-- Save plot objects after adjustment
	if _G.PlotObjectSaver then
		task.spawn(function()
			_G.PlotObjectSaver.savePlayerObjects(player)
		end)
	end

	-- Send success response to client
	local adjustResponseEvent = ReplicatedStorage:WaitForChild("Events"):WaitForChild("AdjustObjectResponse")
	adjustResponseEvent:FireClient(player, true, "Object adjusted successfully")
end)

-- Handle DeleteObject event (removing objects from plots)
-- Create the DeleteObject event if it doesn't exist
local eventsFolder = ReplicatedStorage:FindFirstChild("Events")
if not eventsFolder then
	eventsFolder = Instance.new("Folder")
	eventsFolder.Name = "Events"
	eventsFolder.Parent = ReplicatedStorage
end

local deleteObjectEvent = eventsFolder:FindFirstChild("DeleteObject")
if not deleteObjectEvent then
	deleteObjectEvent = Instance.new("RemoteEvent")
	deleteObjectEvent.Name = "DeleteObject"
	deleteObjectEvent.Parent = eventsFolder
end

deleteObjectEvent.OnServerEvent:Connect(function(player, objectName)
	print("Player", player.Name, "wants to delete", objectName)

	-- Get player's plot folder
	local playerPlotFolder = getPlayerPlotFolder(player)
	if not playerPlotFolder then
		warn("Could not find plot folder for player", player.Name)
		return
	end

	-- Find the Objects folder within the player's plot
	local objectsFolder = playerPlotFolder:FindFirstChild("Objects")
	if not objectsFolder then
		warn("No Objects folder found in", player.Name .. "'s plot")
		return
	end

	-- Find the specific object to delete
	local objectToDelete = objectsFolder:FindFirstChild(objectName)
	if not objectToDelete then
		warn("Object", objectName, "not found in", player.Name .. "'s plot")
		return
	end

	if not objectToDelete:IsA("Model") then
		warn("Object", objectName, "is not a Model")
		return
	end

	-- Extract the base object name (remove timestamp suffix)
	local baseObjectName = objectName:match("^(.-)_") or objectName

	-- Delete the object from the plot
	objectToDelete:Destroy()
	print("Successfully deleted", objectName, "from", player.Name .. "'s plot")

	-- Save plot objects after deletion
	if _G.PlotObjectSaver then
		task.spawn(function()
			_G.PlotObjectSaver.savePlayerObjects(player)
		end)
	end

	-- Note: Objects are not added back to inventory when removed - they are permanently deleted
end)