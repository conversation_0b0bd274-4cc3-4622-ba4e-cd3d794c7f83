{"name": "NewGame1", "className": "DataModel", "filePaths": ["default.project.json"], "children": [{"name": "ReplicatedStorage", "className": "ReplicatedStorage", "children": [{"name": "Shared", "className": "Folder", "children": [{"name": "PlantConfigure", "className": "ModuleScript", "filePaths": ["src/shared\\PlantConfigure.lua"]}]}]}, {"name": "ServerScriptService", "className": "ServerScriptService", "children": [{"name": "Server", "className": "Folder", "children": [{"name": "EventHandler", "className": "<PERSON><PERSON><PERSON>", "filePaths": ["src/server\\EventHandler.server.luau"]}, {"name": "plants", "className": "<PERSON><PERSON><PERSON>", "filePaths": ["src/server\\plants.server.lua"]}]}, {"name": "onJoin", "className": "Folder", "children": [{"name": "Datastore", "className": "<PERSON><PERSON><PERSON>", "filePaths": ["src/onJoin\\Datastore.server.lua"]}]}]}, {"name": "StarterPlayer", "className": "StarterPlayer", "children": [{"name": "StarterPlayerScripts", "className": "StarterPlayerScripts", "children": [{"name": "Client", "className": "Folder", "children": [{"name": "onJoin", "className": "Folder", "children": [{"name": "onJoin", "className": "LocalScript", "filePaths": ["src/client\\onJoin\\onJoin.client.luau"]}]}]}]}]}]}