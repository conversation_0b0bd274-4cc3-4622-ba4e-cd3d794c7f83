-- Economy Analytics Module for tracking money transactions
local AnalyticsService = game:GetService("AnalyticsService")

local EconomyAnalytics = {}

-- Core analytics logging function
local function logEconomyEvent(player, eventType, currencyType, amount, context)
    if not player or not player.Parent then return end
    
    local success, errorMessage = pcall(function()
        AnalyticsService:LogEconomyEvent(
            player,
            eventType, -- Enum.AnalyticsEconomyFlowType (Sink or Source)
            currencyType, -- String: name of currency (e.g., "Money", "Coins")
            amount, -- Number: amount of currency
            context -- Table: additional context data
        )
    end)
    
    if not success then
        warn("Failed to log economy event:", errorMessage)
    end
end

-- Helper function to log money earned (Source)
function EconomyAnalytics.LogMoneyEarned(player, amount, source, details)
    logEconomyEvent(
        player,
        Enum.AnalyticsEconomyFlowType.Source,
        "Money",
        amount,
        {
            source = source, -- e.g., "daily_reward", "object_sale", "quest_completion"
            details = details or {} -- additional context
        }
    )
end

-- Helper function to log money spent (Sink)
function EconomyAnalytics.LogMoneySpent(player, amount, category, item, details)
    logEconomyEvent(
        player,
        Enum.AnalyticsEconomyFlowType.Sink,
        "Money",
        amount,
        {
            category = category, -- e.g., "objects", "upgrades", "cosmetics"
            item = item, -- specific item purchased
            details = details or {} -- additional context
        }
    )
end

-- Specific helper functions for common economy events
function EconomyAnalytics.LogObjectPurchase(player, objectName, cost)
    EconomyAnalytics.LogMoneySpent(player, cost, "objects", objectName, {
        object_type = objectName,
        transaction_type = "purchase"
    })
end

function EconomyAnalytics.LogObjectSale(player, objectName, earnings)
    EconomyAnalytics.LogMoneyEarned(player, earnings, "object_sale", {
        object_type = objectName,
        transaction_type = "sale"
    })
end

function EconomyAnalytics.LogDailyReward(player, amount)
    EconomyAnalytics.LogMoneyEarned(player, amount, "daily_reward", {
        reward_type = "daily_login"
    })
end

function EconomyAnalytics.LogQuestReward(player, amount, questName)
    EconomyAnalytics.LogMoneyEarned(player, amount, "quest_completion", {
        quest_name = questName,
        reward_type = "quest"
    })
end

function EconomyAnalytics.LogUpgradePurchase(player, upgradeName, cost)
    EconomyAnalytics.LogMoneySpent(player, cost, "upgrades", upgradeName, {
        upgrade_type = upgradeName,
        transaction_type = "upgrade"
    })
end

function EconomyAnalytics.LogCosmeticPurchase(player, cosmeticName, cost)
    EconomyAnalytics.LogMoneySpent(player, cost, "cosmetics", cosmeticName, {
        cosmetic_type = cosmeticName,
        transaction_type = "cosmetic"
    })
end

-- Function to log general money changes with context
function EconomyAnalytics.LogMoneyChange(player, oldAmount, newAmount, context)
    local difference = newAmount - oldAmount
    if difference > 0 then
        -- Money increased (Source)
        EconomyAnalytics.LogMoneyEarned(player, difference, context.source or "unknown", {
            previous_amount = oldAmount,
            new_amount = newAmount,
            context = context
        })
    elseif difference < 0 then
        -- Money decreased (Sink)
        EconomyAnalytics.LogMoneySpent(player, math.abs(difference), context.category or "unknown", context.item or "unknown", {
            previous_amount = oldAmount,
            new_amount = newAmount,
            context = context
        })
    end
end

return EconomyAnalytics
