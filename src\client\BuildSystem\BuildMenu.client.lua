-- BuildMenu.client.lua
-- Handles the Blueprints tool and shows/hides the BuildUI when tool is equipped/unequipped

local Players = game:GetService("Players")
local StarterGui = game:GetService("StarterGui")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local player = Players.LocalPlayer

-- Load the ObjectInventory module
local ObjectInventory = require(ReplicatedStorage:WaitForChild("ObjectInventory"))
local TweenService = game:GetService("TweenService")

-- Variables to track current visible frame for dynamic updates
local currentVisibleFrame = nil
local currentVisibleClassName = nil
local currentSectionName = "Machines" -- Default section name
local sectionLabel = nil -- Reference to the SectionLabel UI element
local lastSelectedSection = "Machines" -- Remember the last selected section

-- Button styling constants
local SELECTED_COLOR = Color3.fromRGB(31, 54, 80)
local UNSELECTED_COLOR = Color3.fromRGB(43, 74, 113)
local SELECTED_SIZE_SCALE = 0.95 -- Slightly smaller when selected
local UNSELECTED_SIZE_SCALE = 1.0 -- Normal size when unselected
local TWEEN_INFO = TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)

-- References to subsection buttons
local machinesBtn = nil
local structuresBtn = nil

-- Store original button sizes
local machinesBtnOriginalSize = nil
local structuresBtnOriginalSize = nil

-- Function to update button visual states
local function updateButtonStates(selectedButton)
	-- Update Machines button
	if machinesBtn then
		-- Store original size if not already stored
		if not machinesBtnOriginalSize then
			machinesBtnOriginalSize = machinesBtn.Size
		end

		local isSelected = (selectedButton == "Machines")
		local targetColor = isSelected and SELECTED_COLOR or UNSELECTED_COLOR
		local targetScale = isSelected and SELECTED_SIZE_SCALE or UNSELECTED_SIZE_SCALE
		local targetStrokeTransparency = isSelected and 0.06 or 0.65

		-- Calculate target size
		local targetSize = UDim2.new(
			machinesBtnOriginalSize.X.Scale * targetScale,
			machinesBtnOriginalSize.X.Offset * targetScale,
			machinesBtnOriginalSize.Y.Scale * targetScale,
			machinesBtnOriginalSize.Y.Offset * targetScale
		)

		-- Tween color
		TweenService:Create(machinesBtn, TWEEN_INFO, {
			BackgroundColor3 = targetColor
		}):Play()

		-- Tween size
		TweenService:Create(machinesBtn, TWEEN_INFO, {
			Size = targetSize
		}):Play()

		-- Tween UIStroke transparency
		local machinesStroke = machinesBtn:FindFirstChild("UIStroke")
		if machinesStroke then
			TweenService:Create(machinesStroke, TWEEN_INFO, {
				Transparency = targetStrokeTransparency
			}):Play()
		end
	end

	-- Update Structures button
	if structuresBtn then
		-- Store original size if not already stored
		if not structuresBtnOriginalSize then
			structuresBtnOriginalSize = structuresBtn.Size
		end

		local isSelected = (selectedButton == "Structures")
		local targetColor = isSelected and SELECTED_COLOR or UNSELECTED_COLOR
		local targetScale = isSelected and SELECTED_SIZE_SCALE or UNSELECTED_SIZE_SCALE
		local targetStrokeTransparency = isSelected and 0.06 or 0.65

		-- Calculate target size
		local targetSize = UDim2.new(
			structuresBtnOriginalSize.X.Scale * targetScale,
			structuresBtnOriginalSize.X.Offset * targetScale,
			structuresBtnOriginalSize.Y.Scale * targetScale,
			structuresBtnOriginalSize.Y.Offset * targetScale
		)

		-- Tween color
		TweenService:Create(structuresBtn, TWEEN_INFO, {
			BackgroundColor3 = targetColor
		}):Play()

		-- Tween size
		TweenService:Create(structuresBtn, TWEEN_INFO, {
			Size = targetSize
		}):Play()

		-- Tween UIStroke transparency
		local structuresStroke = structuresBtn:FindFirstChild("UIStroke")
		if structuresStroke then
			TweenService:Create(structuresStroke, TWEEN_INFO, {
				Transparency = targetStrokeTransparency
			}):Play()
		end
	end
end

-- Function to update the SectionLabel text
local function updateSectionLabel(sectionName)
	currentSectionName = sectionName
	lastSelectedSection = sectionName -- Remember this selection
	if sectionLabel then
		sectionLabel.Text = sectionName
		print("Updated SectionLabel to:", sectionName)
	else
		print("SectionLabel not found - cannot update text")
	end

	-- Update button visual states
	updateButtonStates(sectionName)
end



-- Helper function to connect SelectBtn functionality and setup individual object monitoring
local function connectSelectBtn(template, objectName)
	local selectBtn = template:FindFirstChild("SelectBtn")
	if selectBtn then
		selectBtn.MouseButton1Click:Connect(function()
			print("SelectBtn clicked for:", objectName)

			-- Don't allow object selection until plot objects are fully loaded
			if not _G.plotObjectsFullyLoaded then
				print("Cannot select object: plot objects still loading - please wait")
				return
			end

			-- Unequip the Blueprints tool
			if player.Character then
				local humanoid = player.Character:FindFirstChild("Humanoid")
				if humanoid then
					local equippedTool = humanoid:FindFirstChild("Blueprints")
					if equippedTool then
						humanoid:UnequipTools()
						print("Unequipped Blueprints tool")
					end
				end
			end
			
			-- Hide the BuildUI (will also be hidden by tool unequipping, but this ensures it)
			if _G.HideBuildUI then
				_G.HideBuildUI()
			end
			
			-- Start placement process
			if _G.StartPlacement then
				print("Starting placement for:", objectName)
				_G.StartPlacement(objectName)
			else
				warn("StartPlacement function not available - waiting for Placement script to load...")
				-- Try waiting a bit for the script to load
				spawn(function()
					for i = 1, 10 do
						wait(0.1)
						if _G.StartPlacement then
							print("StartPlacement function found after waiting, starting placement for:", objectName)
							_G.StartPlacement(objectName)
							return
						end
					end
					warn("StartPlacement function still not available after waiting")
				end)
			end
		end)
	else
		warn("SelectBtn not found in template for:", objectName)
	end
end

-- Helper function to setup individual object value monitoring for a template
local function setupTemplateObjectMonitoring(template, objectName)
	-- Wait for Objects folder to be created (happens after plot selection)
	local objects = player:WaitForChild("Objects", 30)
	if not objects then
		warn("Player Objects folder not found after 30 seconds")
		return
	end
	
	-- Find the specific object value
	local objectValue = objects:FindFirstChild(objectName)
	if objectValue and objectValue:IsA("IntValue") then
		-- Connect to this specific object's value changes
		local connection
		connection = objectValue.Changed:Connect(function()
			-- Update this template's count display
			local objectNumberLabel = template:FindFirstChild("ObjectNumber")
			if objectNumberLabel then
				objectNumberLabel.Text = "x" .. tostring(objectValue.Value)
				print("Updated template count for:", objectName, "to:", objectValue.Value)

				-- If count reaches 0, remove the template
				if objectValue.Value <= 0 then
					print("Removing template for:", objectName, "- count is 0")
					connection:Disconnect() -- Clean up the connection
					template:Destroy()
				end
			end
		end)
		
		-- Store the connection in the template for cleanup if needed
		template:SetAttribute("ObjectConnection", true)
		
		print("Setup individual monitoring for:", objectName, "template")
	else
		-- Object doesn't exist yet, monitor for it to be added
		local addedConnection
		addedConnection = objects.ChildAdded:Connect(function(child)
			if child.Name == objectName and child:IsA("IntValue") then
				-- Now connect to the newly added object
				child.Changed:Connect(function()
					local objectNumberLabel = template:FindFirstChild("ObjectNumber")
					if objectNumberLabel then
						objectNumberLabel.Text = "x" .. tostring(child.Value)
						print("Updated template count for:", objectName, "to:", child.Value)

						if child.Value <= 0 then
							print("Removing template for:", objectName, "- count is 0")
							template:Destroy()
						end
					end
				end)
				addedConnection:Disconnect() -- Clean up this connection
				print("Connected to newly added object:", objectName)
			end
		end)
	end
end

-- Function to populate a frame with object templates
local function populateFrameWithObjects(frame, className)
	print("Populating", frame.Name, "with", className, "objects...")
	
	-- Wait for ItemTemplate
	local itemTemplate = ReplicatedStorage:WaitForChild("ItemTemplate", 5)
	if not itemTemplate then
		warn("ItemTemplate not found in ReplicatedStorage")
		return
	end
	
	-- Clear existing items (except UIGridLayout)
	for _, child in pairs(frame:GetChildren()) do
		if not child:IsA("UIGridLayout") then
			child:Destroy()
		end
	end
	
	local classObjects = ObjectInventory.getObjectsByClass(className)
	if not classObjects then
		print("No objects found for class:", className)
		return
	end
	
	-- Create template for each object in the class (only if player has > 0)
	for objectName, objectData in pairs(classObjects) do
		local playerCount = ObjectInventory.getObjectCount(player, objectName)
		
		-- Only create template if player has at least 1 of this object
		if playerCount > 0 then
			local template = itemTemplate:Clone()
			template.Name = objectName
			template.Parent = frame
			
			-- Update ObjectName label
			local objectNameLabel = template:FindFirstChild("ObjectName")
			if objectNameLabel then
				objectNameLabel.Text = objectName
			else
				warn("ObjectName label not found in template")
			end
			
			-- Update ObjectNumber label with player's current count
			local objectNumberLabel = template:FindFirstChild("ObjectNumber")
			if objectNumberLabel then
				objectNumberLabel.Text = "x" .. tostring(playerCount)
			else
				warn("ObjectNumber label not found in template")
			end
			
			-- Update ObjectImage with the image ID
			local objectImageLabel = template:FindFirstChild("ObjectImage")
			if objectImageLabel then
				local imageId = ObjectInventory.getObjectImageId(objectName)
				if imageId then
					objectImageLabel.Image = imageId
					print("Set image for:", objectName, "to:", imageId)
				else
					warn("No image ID found for object:", objectName)
				end
			else
				warn("ObjectImage label not found in template")
			end
			
			-- Connect SelectBtn functionality
			connectSelectBtn(template, objectName)
			
			-- Setup individual object monitoring for this template (deferred)
			spawn(function()
				setupTemplateObjectMonitoring(template, objectName)
			end)
			
			print("Created template for:", objectName, "Count:", playerCount)
		else
			print("Skipping template for:", objectName, "- player has 0")
		end
	end
end

-- Function to setup monitoring for existing templates when Objects folder becomes available
local function setupExistingTemplatesMonitoring()
	if not currentVisibleFrame then
		return
	end

	-- Wait for Objects folder to exist first
	local objects = player:WaitForChild("Objects", 30)
	if not objects then
		warn("Player Objects folder not found after 30 seconds - cannot setup existing template monitoring")
		return
	end

	print("Setting up monitoring for existing templates in:", currentVisibleFrame.Name)

	-- Go through all existing templates and set up monitoring
	for _, child in pairs(currentVisibleFrame:GetChildren()) do
		if child:IsA("Frame") or child:IsA("ImageLabel") then -- Template instances
			local objectName = child.Name
			if objectName ~= "UIGridLayout" then -- Skip layout objects
				-- Check if this template already has monitoring set up
				if not child:GetAttribute("ObjectConnection") then
					print("Setting up monitoring for existing template:", objectName)
					spawn(function()
						setupTemplateObjectMonitoring(child, objectName)
					end)
				end
			end
		end
	end
end

-- Function to switch to a specific section
local function switchToSection(sectionName, displayFrame)
	if not displayFrame then
		warn("DisplayFrame not provided to switchToSection")
		return
	end

	-- Hide all frames first
	for _, child in pairs(displayFrame:GetChildren()) do
		if child:IsA("ScrollingFrame") or child:IsA("Frame") then
			child.Visible = false
		end
	end

	-- Switch to the requested section
	if sectionName == "Machines" then
		local machineFrame = displayFrame:FindFirstChild("MachineFrame")
		if machineFrame then
			populateFrameWithObjects(machineFrame, "Machine")
			machineFrame.Visible = true
			currentVisibleFrame = machineFrame
			currentVisibleClassName = "Machine"
			updateSectionLabel("Machines")
			spawn(function()
				setupExistingTemplatesMonitoring()
			end)
			print("Switched to Machines section")
		else
			warn("MachineFrame not found in DisplayFrame")
		end
	elseif sectionName == "Structures" then
		local structureFrame = displayFrame:FindFirstChild("StructureFrame")
		if structureFrame then
			populateFrameWithObjects(structureFrame, "Structure")
			structureFrame.Visible = true
			currentVisibleFrame = structureFrame
			currentVisibleClassName = "Structure"
			updateSectionLabel("Structures")
			spawn(function()
				setupExistingTemplatesMonitoring()
			end)
			print("Switched to Structures section")
		else
			warn("StructureFrame not found in DisplayFrame")
		end
	else
		warn("Unknown section name:", sectionName)
	end
end

-- Function to monitor for new objects being added to inventory and create templates
local function setupNewObjectMonitoring()
	print("Setting up new object monitoring...")
	-- Wait for Objects folder to be created (happens after plot selection)
	local objects = player:WaitForChild("Objects", 30)
	if not objects then
		warn("Player Objects folder not found after 30 seconds")
		return
	end
	print("Objects folder found, setting up monitoring")
	
	-- First, set up monitoring for any existing templates that might not have it yet
	setupExistingTemplatesMonitoring()
	
	-- Helper function to create template for an object
	local function createTemplateForObject(objectName, objectValue)
		-- Check if this object belongs to the currently visible class
		if currentVisibleFrame and currentVisibleClassName then
			local classObjects = ObjectInventory.getObjectsByClass(currentVisibleClassName)
			if classObjects and classObjects[objectName] then
				-- This object belongs to the current visible class
				-- Check if template already exists
				local existingTemplate = currentVisibleFrame:FindFirstChild(objectName)
				if not existingTemplate then
					-- Create new template for this object
					local itemTemplate = ReplicatedStorage:WaitForChild("ItemTemplate", 5)
					if itemTemplate then
						local template = itemTemplate:Clone()
						template.Name = objectName
						template.Parent = currentVisibleFrame
						
						-- Update ObjectName label
						local objectNameLabel = template:FindFirstChild("ObjectName")
						if objectNameLabel then
							objectNameLabel.Text = objectName
						end
						
						-- Update ObjectNumber label
						local objectNumberLabel = template:FindFirstChild("ObjectNumber")
						if objectNumberLabel then
							objectNumberLabel.Text = "x" .. tostring(objectValue)
						end
						
						-- Update ObjectImage with the image ID
						local objectImageLabel = template:FindFirstChild("ObjectImage")
						if objectImageLabel then
							local imageId = ObjectInventory.getObjectImageId(objectName)
							if imageId then
								objectImageLabel.Image = imageId
								print("Set image for:", objectName, "to:", imageId)
							else
								warn("No image ID found for object:", objectName)
							end
						else
							warn("ObjectImage label not found in template")
						end
						
						-- Connect SelectBtn functionality
						connectSelectBtn(template, objectName)
						
						-- Setup individual object monitoring for this template (deferred)
						spawn(function()
							setupTemplateObjectMonitoring(template, objectName)
						end)
						
						print("Created template for object:", objectName, "Count:", objectValue)
					end
				end
			end
		end
	end
	
	-- Monitor changes to existing objects (for 0 -> positive transitions)
	for _, objectValue in pairs(objects:GetChildren()) do
		if objectValue:IsA("IntValue") then
			objectValue.Changed:Connect(function()
				print("Object count changed:", objectValue.Name, "=", objectValue.Value)
				-- If object went from 0 to positive, create template if needed
				if objectValue.Value > 0 then
					createTemplateForObject(objectValue.Name, objectValue.Value)
				end
			end)
		end
	end
	
	-- Monitor for new objects being added
	objects.ChildAdded:Connect(function(child)
		if child:IsA("IntValue") then
			print("New object added to inventory:", child.Name, "with value:", child.Value)
			
			-- If the new object has a positive value, create template
			if child.Value > 0 then
				createTemplateForObject(child.Name, child.Value)
			end
			
			-- Also monitor this new object for future changes
			child.Changed:Connect(function()
				print("New object count changed:", child.Name, "=", child.Value)
				-- If object went from 0 to positive, create template if needed
				if child.Value > 0 then
					createTemplateForObject(child.Name, child.Value)
				end
			end)
		end
	end)
end



-- Setup SubSections buttons for category switching
local function setupSubSectionButtons(buildUI)
	print("Setting up SubSection buttons...")
	
	-- Wait for MainFrame first
	local mainFrame = buildUI:WaitForChild("MainFrame", 5)
	if not mainFrame then
		warn("MainFrame not found in BuildUI")
		return
	end
	print("Found MainFrame:", mainFrame)
	
	-- Wait for SubSections frame
	local subSections = mainFrame:WaitForChild("SubSections", 5)
	if not subSections then
		warn("SubSections not found in BuildUI.MainFrame")
		return
	end
	print("Found SubSections:", subSections)
	
	-- Wait for DisplayFrame
	local displayFrame = mainFrame:WaitForChild("DisplayFrame", 5)
	if not displayFrame then
		warn("DisplayFrame not found in BuildUI.MainFrame")
		return
	end
	print("Found DisplayFrame:", displayFrame)

	-- Find SectionLabel in DisplayFrame
	sectionLabel = displayFrame:FindFirstChild("SectionLabel")
	if sectionLabel then
		print("Found SectionLabel:", sectionLabel)
	else
		warn("SectionLabel not found in BuildUI.MainFrame.DisplayFrame")
	end
	
	-- Debug: Print all children in SubSections
	print("SubSections children:")
	for _, child in pairs(subSections:GetChildren()) do
		print("  -", child.Name, child.ClassName)
	end
	
	-- Debug: Print all children in DisplayFrame
	print("DisplayFrame children:")
	for _, child in pairs(displayFrame:GetChildren()) do
		print("  -", child.Name, child.ClassName)
	end
	
	-- Function to hide all frames in DisplayFrame
	local function hideAllDisplayFrames()
		print("Hiding all display frames...")
		for _, child in pairs(displayFrame:GetChildren()) do
			if child:IsA("ScrollingFrame") or child:IsA("Frame") then
				print("  Hiding frame:", child.Name, "Type:", child.ClassName)
				child.Visible = false
			end
		end
	end
	
	-- Setup Machines button
	machinesBtn = subSections:FindFirstChild("Machines")
	if machinesBtn then
		print("Found Machines button:", machinesBtn)
		machinesBtn.MouseButton1Click:Connect(function()
			print("Machines button clicked!")
			switchToSection("Machines", displayFrame)
		end)
	else
		warn("Machines button not found in SubSections")
	end

	-- Setup Structures button
	structuresBtn = subSections:FindFirstChild("Structures")
	if structuresBtn then
		print("Found Structures button:", structuresBtn)
		structuresBtn.MouseButton1Click:Connect(function()
			print("Structures button clicked!")
			switchToSection("Structures", displayFrame)
		end)
	else
		warn("Structures button not found in SubSections")
	end
	
	-- Initially show the last selected section (defaults to Machines on first run)
	switchToSection(lastSelectedSection, displayFrame)

	-- Set initial button states after a brief delay to ensure buttons are ready
	spawn(function()
		wait(0.1) -- Small delay to ensure buttons are fully loaded
		updateButtonStates(lastSelectedSection)
	end)
end

-- Setup Blueprints tool functionality to show/hide BuildUI
local function setupBlueprintsTool()
	print("Setting up Blueprints tool...")
	
	local playerGui = player:WaitForChild("PlayerGui")
	
	-- Wait for BuildUI
	local buildUI = playerGui:WaitForChild("BuildUI", 10)
	if not buildUI then
		warn("BuildUI not found in PlayerGui")
		return
	end
	print("Found BuildUI:", buildUI)
	
	local buildUIMainFrame = buildUI:WaitForChild("MainFrame", 5)
	if not buildUIMainFrame then
		warn("MainFrame not found in BuildUI")
		return
	end
	print("Found BuildUI MainFrame:", buildUIMainFrame)
	
	-- Initially hide the BuildUI
	buildUIMainFrame.Visible = false
	
	-- Function to show BuildUI
	local function showBuildUI()
		buildUIMainFrame.Visible = true
		-- Restore the last selected section when menu is reopened
		local displayFrame = buildUIMainFrame:FindFirstChild("DisplayFrame")
		if displayFrame then
			-- Make sure we have the SectionLabel reference
			if not sectionLabel then
				sectionLabel = displayFrame:FindFirstChild("SectionLabel")
			end
			switchToSection(lastSelectedSection, displayFrame)
			-- Restore button states
			spawn(function()
				wait(0.1) -- Small delay to ensure buttons are ready
				updateButtonStates(lastSelectedSection)
			end)
		end
		print("BuildUI shown - restored to section:", lastSelectedSection)
	end
	
	-- Function to hide BuildUI
	local function hideBuildUI()
		buildUIMainFrame.Visible = false
		print("BuildUI hidden")
	end
	
	-- Function to handle tool equipped
	local function onToolEquipped(tool)
		if tool.Name == "Blueprints" then
			print("Blueprints tool equipped")
			showBuildUI()
		end
	end
	
	-- Function to handle tool unequipped
	local function onToolUnequipped(tool)
		if tool.Name == "Blueprints" then
			print("Blueprints tool unequipped")
			hideBuildUI()
		end
	end
	
	-- Monitor for tool equipping/unequipping
	player.CharacterAdded:Connect(function(character)
		-- Monitor tools being added to character (equipped)
		character.ChildAdded:Connect(function(child)
			if child:IsA("Tool") then
				onToolEquipped(child)
			end
		end)
		
		-- Monitor tools being removed from character (unequipped)
		character.ChildRemoved:Connect(function(child)
			if child:IsA("Tool") then
				onToolUnequipped(child)
			end
		end)
		
		-- Check if Blueprints tool is already equipped when character spawns
		local blueprintsTool = character:FindFirstChild("Blueprints")
		if blueprintsTool then
			onToolEquipped(blueprintsTool)
		end
	end)
	
	-- Handle if character already exists
	if player.Character then
		local character = player.Character
		
		-- Monitor tools being added to character (equipped)
		character.ChildAdded:Connect(function(child)
			if child:IsA("Tool") then
				onToolEquipped(child)
			end
		end)
		
		-- Monitor tools being removed from character (unequipped)
		character.ChildRemoved:Connect(function(child)
			if child:IsA("Tool") then
				onToolUnequipped(child)
			end
		end)
		
		-- Check if Blueprints tool is already equipped
		local blueprintsTool = character:FindFirstChild("Blueprints")
		if blueprintsTool then
			onToolEquipped(blueprintsTool)
		end
	end
	
	-- Setup SubSections buttons for category switching
	setupSubSectionButtons(buildUI)
	
	-- Setup monitoring for new objects being added to inventory (deferred until Objects folder exists)
	spawn(function()
		setupNewObjectMonitoring()
	end)
	
	-- Make hideBuildUI and showBuildUI functions available globally for placement system
	_G.HideBuildUI = hideBuildUI
	_G.ShowBuildUI = showBuildUI
end

-- Initialize when script runs
print("BuildMenu system starting...")
setupBlueprintsTool()

print("BuildMenu system loaded")