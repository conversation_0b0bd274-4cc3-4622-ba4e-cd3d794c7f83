local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local player = Players.LocalPlayer
local camera = workspace.CurrentCamera

-- Check if we're on a mobile device
local function isMobileDevice()
    return UserInputService.TouchEnabled and not UserInputService.KeyboardEnabled
end

local hasClaimedPlot = false
local plotCheckConnection = nil

-- Mobile plot loading management
local plotLoadingConnections = {}
local forcedLoadedPlots = {}

-- Function to force plots to stay loaded on mobile devices
local function ensurePlotsLoaded(plotsFolder)
    if not isMobileDevice() then return end

    print("Mobile device detected - ensuring plots stay loaded")

    -- Clean up existing connections
    for _, connection in pairs(plotLoadingConnections) do
        if connection then connection:Disconnect() end
    end
    plotLoadingConnections = {}

    -- Get children safely
    local children = plotsFolder:GetChildren()
    if not children then
        warn("Mobile: Could not get children from plots folder")
        return
    end

    -- Force all plots to stay loaded by maintaining references and monitoring them
    for _, plotFolder in pairs(children) do
        local plot = plotFolder:FindFirstChild("Plot")
        if plot then
            -- Store reference to prevent garbage collection
            forcedLoadedPlots[plotFolder.Name] = plot

            -- Monitor for plot unloading and reload if necessary
            local connection = plot.AncestryChanged:Connect(function()
                if not plot.Parent then
                    print("Plot", plotFolder.Name, "was unloaded - attempting to reload")
                    -- Wait a moment then try to re-reference
                    task.wait(0.1)
                    local reloadedPlot = plotFolder:FindFirstChild("Plot")
                    if reloadedPlot then
                        forcedLoadedPlots[plotFolder.Name] = reloadedPlot
                        print("Successfully reloaded plot", plotFolder.Name)
                    else
                        warn("Failed to reload plot", plotFolder.Name)
                    end
                end
            end)
            plotLoadingConnections[plotFolder.Name] = connection
        end
    end
end

-- Function to clean up plot loading management
local function cleanupPlotLoading()
    for _, connection in pairs(plotLoadingConnections) do
        if connection then connection:Disconnect() end
    end
    plotLoadingConnections = {}
    forcedLoadedPlots = {}
end

-- Function to validate and attempt to reload a plot on mobile
local function validatePlotForMobile(plotFolder)
    if not isMobileDevice() or not plotFolder then return plotFolder:FindFirstChild("Plot") end

    local plot = plotFolder:FindFirstChild("Plot")
    if plot then return plot end

    -- Plot not found, attempt to reload
    print("Mobile: Attempting to reload plot", plotFolder.Name)

    -- Try multiple reload strategies
    for attempt = 1, 3 do
        task.wait(0.1 * attempt) -- Increasing delay
        plot = plotFolder:FindFirstChild("Plot")
        if plot then
            print("Mobile: Successfully reloaded plot", plotFolder.Name, "on attempt", attempt)
            return plot
        end
    end

    warn("Mobile: Failed to reload plot", plotFolder.Name, "after 3 attempts")
    return nil
end

-- Function to set player walk speed
local function setPlayerWalkSpeed(speed)
    local function applySpeed()
        if player.Character and player.Character:FindFirstChild("Humanoid") then
            player.Character.Humanoid.WalkSpeed = speed
            print("Set walkspeed to:", speed)
            return true
        end
        return false
    end
    
    -- Try to apply immediately
    if not applySpeed() then
        -- If character not ready, wait for it
        local connection
        connection = player.CharacterAdded:Connect(function(character)
            local humanoid = character:WaitForChild("Humanoid")
            humanoid.WalkSpeed = speed
            print("Set walkspeed to:", speed, "(after character spawn)")
            connection:Disconnect()
        end)
        
        -- Also try again after a short delay in case character exists but humanoid isn't ready
        spawn(function()
            wait(0.5)
            applySpeed()
        end)
    end
end

-- Returns the center (Vector3) and radius (number) of all plots in workspace
local function getPlotsCenterAndRadius()
    local plotsFolder = workspace:FindFirstChild("Plots")
    if not plotsFolder then
        warn("No Plots folder found in workspace!")
        return Vector3.new(0, 0, 0), 100
    end
    
    -- Wait for plots to load with timeout and stability check
    local plotPositions = {}
    local maxWaitTime = 12 -- Even longer wait time
    local startTime = tick()
    local stableCount = 0
    local lastPlotCount = 0
    local stabilityRequired = 5 -- More stability checks required
    local minimumPlots = 2 -- Require at least 2 plots before considering stable
    
    print("Waiting for all plots to load...")
    
    repeat
        plotPositions = {}
        for _, plotFolder in ipairs(plotsFolder:GetChildren()) do
            if plotFolder:FindFirstChild("Plot") then
                -- Double-check that the plot actually has a valid position
                local plot = plotFolder.Plot
                if plot.Position and plot.Position.Magnitude > 0 then
                    table.insert(plotPositions, plot.Position)
                end
            end
        end
        
        -- Check if plot count is stable (not changing) AND meets minimum requirement
        if #plotPositions == lastPlotCount and #plotPositions >= minimumPlots then
            stableCount = stableCount + 1
            print("Stability check", stableCount, "of", stabilityRequired, "- Found", #plotPositions, "plots")
        else
            stableCount = 0 -- Reset if count changed or below minimum
            if #plotPositions ~= lastPlotCount then
                print("Plot count changed from", lastPlotCount, "to", #plotPositions)
            end
        end
        
        lastPlotCount = #plotPositions
        
        -- Exit if we have stable plot count for required checks
        if stableCount >= stabilityRequired then
            print("Plot count stabilized at", #plotPositions, "plots - Starting camera orbit!")
            break
        end
        
        wait(0.3) -- Longer wait between checks
    until tick() - startTime > maxWaitTime
    
    if #plotPositions == 0 then
        warn("No plots found in Plots folder after waiting!")
        return Vector3.new(0, 0, 0), 100
    elseif #plotPositions == 1 then
        warn("Only 1 plot found - camera may orbit around single plot!")
    end
    
    print("Final plot count:", #plotPositions, "plots for camera orbit calculation")
    
    -- Calculate center
    local sum = Vector3.new(0, 0, 0)
    for _, pos in ipairs(plotPositions) do
        sum = sum + pos
    end
    local center = sum / #plotPositions
    
    -- Calculate max distance from center (for radius)
    local maxDist = 0
    for _, pos in ipairs(plotPositions) do
        local dist = (pos - center).Magnitude
        if dist > maxDist then
            maxDist = dist
        end
    end
    local radius = math.max(maxDist + 60, 100) -- Ensure minimum radius of 100
    
    -- Additional validation: if radius is too small with multiple plots, something went wrong
    if #plotPositions > 1 and radius < 150 then
        warn("Radius seems too small for", #plotPositions, "plots. Recalculating...")
        -- Force a larger radius to ensure we see all plots
        radius = math.max(radius * 2, 200)
    end
    
    print("Camera orbit center:", center, "radius:", radius)
    return center, radius
end

local function isPlotClaimed(plotFolder)
    local values = plotFolder:FindFirstChild("Values")
    local ownerId = values and values:FindFirstChild("OwnerId")
    return ownerId and ownerId.Value ~= 0 and ownerId.Value ~= nil
end

local function getNextUnclaimedIndex(startIndex, plotFolders)
    local n = #plotFolders
    local idx = startIndex
    for i = 1, n do
        if not isPlotClaimed(plotFolders[idx]) then
            return idx
        end
        idx = idx + 1
        if idx > n then idx = 1 end
    end
    return nil -- all claimed
end

local function getPrevUnclaimedIndex(startIndex, plotFolders)
    local n = #plotFolders
    local idx = startIndex
    for i = 1, n do
        if not isPlotClaimed(plotFolders[idx]) then
            return idx
        end
        idx = idx - 1
        if idx < 1 then idx = n end
    end
    return nil -- all claimed
end

-- Plot highlighting system
local currentHighlight = nil

local function createPlotHighlight(plot)
    -- Create a Roblox Highlight object for the plot
    local highlight = Instance.new("Highlight")
    highlight.Name = "PlotHighlight"
    highlight.FillColor = Color3.fromRGB(0, 162, 255) -- Bright blue color
    highlight.OutlineColor = Color3.fromRGB(255, 255, 255) -- White outline
    highlight.FillTransparency = 0.75
    highlight.OutlineTransparency = 0
    highlight.DepthMode = Enum.HighlightDepthMode.AlwaysOnTop
    
    highlight.Parent = plot
    return highlight
end

local function setPlotHighlight(plotFolder)
    -- Remove existing highlight
    if currentHighlight then
        currentHighlight:Destroy()
        currentHighlight = nil
    end
    
    -- Add new highlight if plot exists
    if plotFolder and plotFolder:FindFirstChild("Plot") then
        local plot = plotFolder.Plot
        currentHighlight = createPlotHighlight(plot)
        print("Added blue highlight to plot:", plotFolder.Name)
    end
end

local function removeCurrentHighlight()
    if currentHighlight then
        currentHighlight:Destroy()
        currentHighlight = nil
        print("Removed plot highlight")
    end
end

local function startSelection()
    local playerGui = player:WaitForChild("PlayerGui")
    local selectionUI = playerGui:WaitForChild("Selection")
    selectionUI.Enabled = true

    local plotsFolder = workspace:WaitForChild("Plots")

    -- Get plot folders first with safety checks
    local plotFolders = plotsFolder:GetChildren()
    if not plotFolders or #plotFolders == 0 then
        warn("No plot folders found in Plots workspace or failed to get children")

        -- On mobile, wait a bit and try again
        if isMobileDevice() then
            print("Mobile: Waiting for plots to load...")
            task.wait(1)
            plotFolders = plotsFolder:GetChildren()

            if not plotFolders or #plotFolders == 0 then
                warn("Mobile: Still no plots found after waiting")
                return
            else
                print("Mobile: Successfully loaded", #plotFolders, "plots after waiting")
            end
        else
            return
        end
    end

    table.sort(plotFolders, function(a, b) return a.Name < b.Name end)

    -- Ensure plots stay loaded on mobile devices
    ensurePlotsLoaded(plotsFolder)

    -- On mobile, pre-validate all plots to catch loading issues early
    if isMobileDevice() then
        print("Mobile: Pre-validating all plots...")
        for i, plotFolder in ipairs(plotFolders) do
            local plot = validatePlotForMobile(plotFolder)
            if not plot then
                warn("Mobile: Plot", plotFolder.Name, "failed pre-validation")
            end
        end
        print("Mobile: Plot pre-validation complete")
    end

    -- Debug: Check plot structure
    print("=== PLOT DEBUG INFO ===")
    print("Total plot folders found:", #plotFolders)
    if isMobileDevice() then
        print("Mobile device detected - plot loading management active")
    end
    for i, plotFolder in ipairs(plotFolders) do
        local hasPlot = plotFolder:FindFirstChild("Plot") ~= nil
        local hasValues = plotFolder:FindFirstChild("Values") ~= nil
        local claimed = "unknown"
        if hasValues then
            local ownerId = plotFolder.Values:FindFirstChild("OwnerId")
            if ownerId then
                claimed = tostring(ownerId.Value ~= 0 and ownerId.Value ~= nil)
            end
        end
        print(string.format("Plot %d: %s | HasPlot: %s | HasValues: %s | Claimed: %s", 
            i, plotFolder.Name, tostring(hasPlot), tostring(hasValues), claimed))
    end
    print("=======================")

    local currentIndex = getNextUnclaimedIndex(1, plotFolders) or 1
    print("Starting at plot index:", currentIndex)
    local cameraTween = nil

    local function moveCameraToPlot(index)
        local plotFolder = plotFolders[index]
        local plot = nil

        -- Mobile-specific plot loading handling
        if isMobileDevice() and plotFolder then
            -- First try to get plot normally
            plot = plotFolder:FindFirstChild("Plot")

            -- If plot not found on mobile, try to force reload it
            if not plot then
                print("Mobile: Plot not loaded for", plotFolder.Name, "- attempting to force load")

                -- Try to trigger plot loading by briefly moving camera closer
                if plotFolder:FindFirstChild("Values") then
                    -- Use plot folder position as fallback
                    local tempPos = Vector3.new(0, 100, 0) -- Default position
                    camera.CFrame = CFrame.new(tempPos)

                    -- Wait a moment for potential loading
                    task.wait(0.2)
                    plot = plotFolder:FindFirstChild("Plot")

                    if not plot then
                        warn("Mobile: Failed to load plot", plotFolder.Name, "after force attempt")
                        return
                    else
                        print("Mobile: Successfully loaded plot", plotFolder.Name)
                    end
                end
            end
        elseif plotFolder then
            plot = plotFolder:FindFirstChild("Plot")
        end

        if plotFolder and plot then
            print("Moving camera to plot", index, "(" .. plotFolder.Name .. ") at position:", plot.Position)
            camera.CameraType = Enum.CameraType.Scriptable

            -- Add blue highlight to the current plot
            setPlotHighlight(plotFolder)

            -- Position camera to always show the plot's front at the bottom of screen
            local cameraHeight = 120
            local cameraDistance = 80 -- Distance back from plot center

            -- Get the plot's front direction (negative LookVector points to the front)
            local plotFront = -plot.CFrame.LookVector

            -- Position camera behind the plot (opposite of front direction) and above
            local cameraPos = plot.Position + (plotFront * -cameraDistance) + Vector3.new(0, cameraHeight, 0)

            -- Make camera look at the plot center, with the front of the plot at bottom of screen
            local targetCFrame = CFrame.lookAt(cameraPos, plot.Position, Vector3.new(0, 1, 0))

            if cameraTween then cameraTween:Cancel() end
            cameraTween = TweenService:Create(camera, TweenInfo.new(0.7, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {CFrame = targetCFrame})
            cameraTween:Play()
        else
            print("ERROR: Cannot move to plot", index, "- plotFolder:", plotFolder and plotFolder.Name or "nil", "hasPlot:", plot ~= nil)

            -- On mobile, try to recover by moving to next available plot
            if isMobileDevice() and plotFolder then
                print("Mobile: Attempting to recover by finding next available plot")
                local nextIndex = getNextUnclaimedIndex(index + 1 > #plotFolders and 1 or index + 1, plotFolders)
                if nextIndex and nextIndex ~= index then
                    currentIndex = nextIndex
                    task.wait(0.1) -- Brief delay before retry
                    moveCameraToPlot(currentIndex)
                end
            end
        end
    end

    moveCameraToPlot(currentIndex)

    selectionUI.MainFrame.LeftBtn.MouseButton1Click:Connect(function()
        if hasClaimedPlot then return end
        local oldIndex = currentIndex
        local searchStart = currentIndex - 1 < 1 and #plotFolders or currentIndex - 1
        currentIndex = getPrevUnclaimedIndex(searchStart, plotFolders) or currentIndex
        print("LEFT CLICK: From", oldIndex, "to", currentIndex, "(searched from", searchStart, ")")
        moveCameraToPlot(currentIndex)
    end)

    selectionUI.MainFrame.RightBtn.MouseButton1Click:Connect(function()
        if hasClaimedPlot then return end
        local oldIndex = currentIndex
        local searchStart = currentIndex + 1 > #plotFolders and 1 or currentIndex + 1
        currentIndex = getNextUnclaimedIndex(searchStart, plotFolders) or currentIndex
        print("RIGHT CLICK: From", oldIndex, "to", currentIndex, "(searched from", searchStart, ")")
        moveCameraToPlot(currentIndex)
    end)

    -- Function to handle plot setup after objects are loaded
    local function proceedWithPlotSetup(plotFolder)
        -- Teleport the player to the plot before tweening the camera
        local plot = plotFolder:FindFirstChild("Plot")

        -- Mobile-specific plot loading for teleportation
        if isMobileDevice() and not plot then
            print("Mobile: Plot not loaded for teleportation - attempting to force load")
            -- Try to force load the plot
            task.wait(0.2)
            plot = plotFolder:FindFirstChild("Plot")

            if not plot then
                warn("Mobile: Failed to load plot for teleportation, using fallback")
                -- Use a fallback position if plot still not loaded
                if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
                    player.Character.HumanoidRootPart.CFrame = CFrame.new(Vector3.new(0, 10, 0))
                end
            end
        end

        if player.Character and player.Character:FindFirstChild("HumanoidRootPart") and plot then
            local size = plot.Size or Vector3.new(0,0,0)
            local offset = (size.Z/2) + 10
            local frontOffset = plot.CFrame.LookVector * offset
            local teleportPosition = plot.Position + frontOffset
            player.Character.HumanoidRootPart.CFrame = CFrame.new(teleportPosition, plot.Position)
            print("Successfully teleported player to plot", plotFolder.Name)
        elseif not plot then
            warn("Failed to teleport player - plot not available for", plotFolder.Name)
        end

        -- Tween camera to character facing the player's direction
        if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
            local hrp = player.Character.HumanoidRootPart

            -- Position camera behind the player facing the same direction
            local cameraHeight = 25 -- Height above the player
            local cameraDistance = 30 -- Distance behind the player

            -- Get the player's facing direction
            local playerFacing = hrp.CFrame.LookVector

            -- Position camera behind the player and above
            local cameraPos = hrp.Position + (playerFacing * -cameraDistance) + Vector3.new(0, cameraHeight, 0)

            -- Make camera look in the same direction as the player
            local lookAtPos = hrp.Position + (playerFacing * 50) -- Look ahead of the player
            local targetCFrame = CFrame.lookAt(cameraPos, lookAtPos, Vector3.new(0, 1, 0))

            local tween = TweenService:Create(camera, TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {CFrame = targetCFrame})
            tween:Play()
            tween.Completed:Wait()
        end

        camera.CameraType = Enum.CameraType.Custom
        -- Restore player movement after plot selection
        setPlayerWalkSpeed(16) -- Restore normal walkspeed

        -- Also ensure walkspeed is maintained on respawn
        local respawnConnection
        respawnConnection = player.CharacterAdded:Connect(function(character)
            local humanoid = character:WaitForChild("Humanoid")
            humanoid.WalkSpeed = 16
            print("Restored walkspeed after respawn")
        end)

        -- Disconnect the plot check connection to save resources
        if plotCheckConnection then
            plotCheckConnection:Disconnect()
            plotCheckConnection = nil
        end

        -- Clean up mobile plot loading management
        cleanupPlotLoading()

        -- Enable tool interactions now that everything is loaded
        _G.plotObjectsFullyLoaded = true
        print("Plot setup complete - tools are now enabled")

        -- Trigger toolbar to show now that camera is at character
        if _G.refreshToolInventory then
            _G.refreshToolInventory()
            print("Triggered toolbar refresh after camera moved to character")
        end
    end

    selectionUI.MainFrame.SelectBtn.MouseButton1Click:Connect(function()
        if hasClaimedPlot then return end
        local plotFolder = plotFolders[currentIndex]
        if plotFolder and not isPlotClaimed(plotFolder) then
            local event = ReplicatedStorage:WaitForChild("Events"):WaitForChild("PlotSelectionEvent")
            event:FireServer(plotFolder.Name)
            hasClaimedPlot = true
            selectionUI.Enabled = false

            -- Remove the plot highlight since selection is complete
            removeCurrentHighlight()

            -- Move camera to birds-eye view over the plot while loading
            local plot = plotFolder:FindFirstChild("Plot")
            if plot then
                print("Moving camera to birds-eye view over plot during loading...")
                local plotCenter = plot.Position
                local birdsEyeHeight = 80 -- Height above the plot
                local cameraPos = plotCenter + Vector3.new(0, birdsEyeHeight, 0)

                -- Calculate the shortest rotation to look down at the plot
                -- Use the current camera's right vector to maintain orientation and avoid spinning
                local currentCFrame = camera.CFrame
                local currentRightVector = currentCFrame.RightVector

                -- Create birds-eye CFrame looking down with minimal rotation
                local downVector = Vector3.new(0, -1, 0) -- Looking straight down
                local upVector = currentRightVector:Cross(downVector).Unit -- Perpendicular to both
                local rightVector = downVector:Cross(upVector).Unit

                local targetCFrame = CFrame.fromMatrix(cameraPos, rightVector, upVector, -downVector)

                local birdsEyeTween = TweenService:Create(camera, TweenInfo.new(0.8, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {CFrame = targetCFrame})
                birdsEyeTween:Play()
            end

            -- Set up listener for plot objects loaded event with mandatory 5-second wait
            local ReplicatedStorage = game:GetService("ReplicatedStorage")
            local eventsFolder = ReplicatedStorage:WaitForChild("Events")
            local plotObjectsLoadedEvent = eventsFolder:WaitForChild("PlotObjectsLoadedEvent")

            local objectsLoaded = false
            local startTime = tick()

            local objectsLoadedConnection
            objectsLoadedConnection = plotObjectsLoadedEvent.OnClientEvent:Connect(function()
                print("Plot objects fully loaded - but waiting for mandatory 5 seconds")
                objectsLoaded = true
                objectsLoadedConnection:Disconnect()
            end)

            -- Wait for both objects to load AND mandatory 5 seconds to pass
            spawn(function()
                while true do
                    local timeElapsed = tick() - startTime

                    if objectsLoaded and timeElapsed >= 5 then
                        print("Mandatory 5 seconds completed - proceeding with camera tween to character")
                        proceedWithPlotSetup(plotFolder)
                        break
                    elseif timeElapsed >= 5 and not objectsLoaded then
                        print("5 seconds elapsed but objects not loaded yet - waiting for objects...")
                    end

                    wait(0.1)
                end
            end)

            print("Plot selected - waiting 5 seconds minimum for objects to load...")
        end
    end)



    -- Constantly check if the current plot is claimed, and move if so
    if plotCheckConnection then
        plotCheckConnection:Disconnect()
    end
    plotCheckConnection = RunService.RenderStepped:Connect(function()
        if hasClaimedPlot then return end
        local plotFolder = plotFolders[currentIndex]
        if plotFolder and isPlotClaimed(plotFolder) then
            local nextIdx = getNextUnclaimedIndex(currentIndex + 1 > #plotFolders and 1 or currentIndex + 1, plotFolders)
            if nextIdx and nextIdx ~= currentIndex then
                currentIndex = nextIdx
                moveCameraToPlot(currentIndex)
            end
        end
    end)

    -- Keyboard input for PC (A/D keys)
    local function onInputBegan(input, gameProcessed)
        if hasClaimedPlot or gameProcessed then return end
        if input.UserInputType == Enum.UserInputType.Keyboard then
            if input.KeyCode == Enum.KeyCode.A then
                currentIndex = getPrevUnclaimedIndex(currentIndex - 1 < 1 and #plotFolders or currentIndex - 1, plotFolders) or currentIndex
                moveCameraToPlot(currentIndex)
            elseif input.KeyCode == Enum.KeyCode.D then
                currentIndex = getNextUnclaimedIndex(currentIndex + 1 > #plotFolders and 1 or currentIndex + 1, plotFolders) or currentIndex
                moveCameraToPlot(currentIndex)
            end
        end
    end
    UserInputService.InputBegan:Connect(onInputBegan)

    -- Animate MainFrame background color on hover
    local mainFrame = selectionUI.MainFrame
    local leftBtn = mainFrame.LeftBtn
    local rightBtn = mainFrame.RightBtn

    local function animateBgColor(guiObject, hoverColor, normalColor)
        guiObject.MouseEnter:Connect(function()
            TweenService:Create(guiObject, TweenInfo.new(0.2), {BackgroundColor3 = hoverColor}):Play()
        end)
        guiObject.MouseLeave:Connect(function()
            TweenService:Create(guiObject, TweenInfo.new(0.2), {BackgroundColor3 = normalColor}):Play()
        end)
    end

    animateBgColor(mainFrame, Color3.fromRGB(41, 95, 54), Color3.fromRGB(50, 117, 67))
    animateBgColor(leftBtn, Color3.fromRGB(41, 95, 54), Color3.fromRGB(50, 117, 67))
    animateBgColor(rightBtn, Color3.fromRGB(41, 95, 54), Color3.fromRGB(50, 117, 67))
end



-- Tween camera to a plot position
local function tweenCameraToPlot(plot)
    -- Position camera to always show the plot's front at the bottom of screen
    local cameraHeight = 120
    local cameraDistance = 80 -- Distance back from plot center
    
    -- Get the plot's front direction (negative LookVector points to the front)
    local plotFront = -plot.CFrame.LookVector
    
    -- Position camera behind the plot (opposite of front direction) and above
    local cameraPos = plot.Position + (plotFront * -cameraDistance) + Vector3.new(0, cameraHeight, 0)
    
    -- Make camera look at the plot center, with the front of the plot at bottom of screen
    local targetCFrame = CFrame.lookAt(cameraPos, plot.Position, Vector3.new(0, 1, 0))
    
    local tween = TweenService:Create(camera, TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {CFrame = targetCFrame})
    tween:Play()
    return tween
end

-- Intro screen and camera circle logic
local Lighting = game:GetService("Lighting")
local introMenu = player:WaitForChild("PlayerGui"):WaitForChild("JoinMenu")
local introMain = introMenu:WaitForChild("MainFrame")
local playBtn = introMain:WaitForChild("PlayBtn")

-- Loading screen elements
local loadFrame = introMenu:WaitForChild("LoadFrame")
local loadInfo = loadFrame:WaitForChild("LoadInfo")

introMenu.Enabled = false -- ensure disabled by default

local blur = Instance.new("BlurEffect")
blur.Size = 20 -- Default blur size
blur.Parent = Lighting
blur.Enabled = false

-- Loading text animation variables
local loadingDots = 1
local loadingTextConnection = nil

-- Function to animate loading text dots
local function startLoadingTextAnimation()
	if loadingTextConnection then
		loadingTextConnection:Disconnect()
	end

	loadingTextConnection = RunService.Heartbeat:Connect(function()
		local dots = string.rep(".", loadingDots)
		loadInfo.Text = "DATA LOADING" .. dots

		-- Cycle through 1, 2, 3 dots
		loadingDots = loadingDots + 1
		if loadingDots > 3 then
			loadingDots = 1
		end

		wait(0.5) -- Change dots every 0.5 seconds
	end)
end

local function stopLoadingTextAnimation()
	if loadingTextConnection then
		loadingTextConnection:Disconnect()
		loadingTextConnection = nil
	end
end

-- Camera orbit logic for intro screen
local orbiting = false
local orbitConn = nil
local function startCameraOrbit(center, radius)
    orbiting = true
    local startTick = tick()
    local yBase = 80
    local yVar = 20
    
    orbitConn = RunService.RenderStepped:Connect(function()
        if not orbiting then
            if orbitConn then
                orbitConn:Disconnect()
            end
            return
        end
        
        local t = (tick() - startTick) * 0.05 -- much slower orbit speed
        local angle = math.rad(360 * t)
        local yAngle = yBase + math.sin(t * 0.7 + math.cos(t * 0.3)) * yVar
        local camPos = center + Vector3.new(math.cos(angle) * radius, yAngle, math.sin(angle) * radius)
        camera.CameraType = Enum.CameraType.Scriptable
        camera.CFrame = CFrame.new(camPos, center + Vector3.new(0, 20, 0))
    end)
end

local function stopCameraOrbit()
    orbiting = false
    if orbitConn then
        orbitConn:Disconnect()
    end
end

-- Ensure selection UI is hidden until Play is pressed
local selectionUI = player:WaitForChild("PlayerGui"):FindFirstChild("Selection")
if selectionUI then
    selectionUI.Enabled = false
end





-- Start with only LoadFrame visible
introMenu.Enabled = true
loadFrame.Visible = true -- Show loading frame
introMain.Visible = false -- Hide main frame initially
setPlayerWalkSpeed(0) -- Disable movement during loading

-- Variable to track if play button is ready to be clicked
local playButtonReady = false

-- Global flag to disable tool interactions until plot objects are fully loaded
_G.plotObjectsFullyLoaded = false

-- Function to request and wait for data pre-loading
local function requestAndWaitForDataPreload()
    print("Requesting data pre-load from server...")

    -- Update loading text to show data loading phase
    if loadInfo then
        loadInfo.Text = "Loading player data..."
    end

    -- Get the remote events
    local ReplicatedStorage = game:GetService("ReplicatedStorage")
    local eventsFolder = ReplicatedStorage:WaitForChild("Events")
    local preloadDataEvent = eventsFolder:WaitForChild("PreloadDataEvent")
    local dataReadyEvent = eventsFolder:WaitForChild("DataReadyEvent")

    -- Set up listener for data ready event
    local dataLoaded = false
    local dataReadyConnection

    dataReadyConnection = dataReadyEvent.OnClientEvent:Connect(function()
        print("Data pre-loading completed!")
        dataLoaded = true
        dataReadyConnection:Disconnect()

        -- Update loading text to show completion
        if loadInfo then
            loadInfo.Text = "Data loaded successfully!"
        end
    end)

    -- Request data pre-loading from server
    preloadDataEvent:FireServer()

    -- Wait for data to be loaded (with timeout)
    local startTime = tick()
    local timeout = 15 -- 15 second timeout

    while not dataLoaded and (tick() - startTime) < timeout do
        wait(0.1)
    end

    if not dataLoaded then
        warn("Data pre-loading timed out after", timeout, "seconds")
        if loadInfo then
            loadInfo.Text = "Data loading timed out - continuing anyway"
        end
        if dataReadyConnection then
            dataReadyConnection:Disconnect()
        end
        return false
    end

    print("Data pre-loading successful!")
    return true
end



-- Start loading text animation
startLoadingTextAnimation()

-- Initialize loading sequence
spawn(function()
    print("Starting game loading sequence...")

    -- Wait for workspace elements to load
    workspace:WaitForChild("Map", 5)
    wait(0.5)

    -- Request and wait for data pre-loading
    local dataPreloaded = requestAndWaitForDataPreload()
    if dataPreloaded then
        print("Player data pre-loaded successfully during loading screen")
    else
        warn("Player data pre-loading failed - will load normally after plot selection")
    end

    -- Load plots
    local center, radius
    local maxRetries = 2
    local retryCount = 0

    repeat
        -- Wait for plots to fully load
        center, radius = getPlotsCenterAndRadius()

        -- Check if we got a suspicious result (likely single plot)
        local plotsFolder = workspace:FindFirstChild("Plots")
        local actualPlotCount = 0
        if plotsFolder then
            for _, plotFolder in ipairs(plotsFolder:GetChildren()) do
                if plotFolder:FindFirstChild("Plot") then
                    actualPlotCount = actualPlotCount + 1
                end
            end
        end

        -- If we have multiple plots but small radius, retry
        if actualPlotCount > 1 and radius < 150 and retryCount < maxRetries then
            retryCount = retryCount + 1
            wait(2) -- Wait longer before retry
        else
            break -- Either good result or max retries reached
        end
    until retryCount >= maxRetries

    -- Validate that we got reasonable values
    if center.Magnitude == 0 and radius == 100 then
        warn("Camera orbit using fallback values - plots may not be loaded properly")
    else
        print("Plots loaded successfully!")
    end

    wait(1) -- Additional loading time

    -- Stop loading text animation
    stopLoadingTextAnimation()

    -- Pre-start blur and orbit in background
    blur.Enabled = true
    blur.Size = 20
    camera.CameraType = Enum.CameraType.Scriptable
    startCameraOrbit(center, radius)

    -- Fade out LoadFrame
    local fadeOutTween = TweenService:Create(
        loadFrame,
        TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
        {BackgroundTransparency = 1}
    )

    local textFadeOutTween = TweenService:Create(
        loadInfo,
        TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
        {TextTransparency = 1}
    )

    fadeOutTween:Play()
    textFadeOutTween:Play()

    fadeOutTween.Completed:Wait()
    loadFrame.Visible = false

    -- Show main frame and fade in play button
    introMain.Visible = true
    playBtn.TextTransparency = 1
    playBtn.BackgroundTransparency = 1

    local playBtnFadeIn = TweenService:Create(
        playBtn,
        TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
        {TextTransparency = 0, BackgroundTransparency = 0}
    )

    playBtnFadeIn:Play()
    playBtnFadeIn.Completed:Wait()

    playButtonReady = true
    print("Loading complete - play button ready, camera orbiting")
end)

playBtn.MouseButton1Click:Connect(function()
    -- Check if play button is ready to be clicked
    if not playButtonReady then
        print("Play button not ready yet - loading still in progress")
        return
    end

    introMenu.Enabled = false
    blur.Enabled = false
    stopCameraOrbit()
    -- Find first open plot
    local plotsFolder = workspace:WaitForChild("Plots")
    local plotFolders = plotsFolder:GetChildren()
    table.sort(plotFolders, function(a, b)
        return a.Name < b.Name
    end)
    local firstIdx = nil
    for i, pf in ipairs(plotFolders) do
        if not isPlotClaimed(pf) then
            firstIdx = i
            break
        end
    end
    local plot = nil
    if firstIdx then
        plot = plotFolders[firstIdx]:FindFirstChild("Plot")
    end
    if plot then
        local tween = tweenCameraToPlot(plot)
        tween.Completed:Wait()
    else
        warn("No open plot found for camera tween.")
    end
    
    -- Restore walkspeed as a safety net in case selection doesn't complete
    setPlayerWalkSpeed(16)
    print("Walkspeed restored after intro sequence")
    
    -- Start selection UI
    if selectionUI then
        selectionUI.Enabled = true
    end
    startSelection()
end)

-- Remove auto-start selection process on join
