-- PlotObjectSaver.lua
-- Module for saving and loading objects on plots with relative positions and orientations

local DataStoreService = game:GetService("DataStoreService")
local Players = game:GetService("Players")

local plotObjectStore = DataStoreService:GetDataStore("PlotObjects")

local PlotObjectSaver = {}

-- Helper function to get plot's CFrame and size
local function getPlotInfo(plotFolder)
    if not plotFolder then return nil end
    
    local plot = plotFolder:FindFirstChild("Plot")
    if not plot or not plot:IsA("BasePart") then return nil end
    
    return {
        cframe = plot.CFrame,
        size = plot.Size,
        position = plot.Position,
        rotation = plot.CFrame - plot.Position -- Get rotation component
    }
end

-- Convert world position/rotation to relative position/rotation based on plot
local function worldToRelative(worldCFrame, plotInfo)
    if not plotInfo then return worldCFrame end

    -- Convert world CFrame to relative CFrame based on plot's coordinate system
    local relativeCFrame = plotInfo.cframe:Inverse() * worldCFrame

    -- Convert to Euler angles for easier storage
    local x, y, z = relativeCFrame:ToEulerAnglesXYZ()

    return {
        position = {
            x = relativeCFrame.Position.X,
            y = relativeCFrame.Position.Y,
            z = relativeCFrame.Position.Z
        },
        rotation = {
            x = x,
            y = y,
            z = z
        }
    }
end

-- Convert relative position/rotation to world position/rotation based on plot
local function relativeToWorld(relativeData, plotInfo)
    if not plotInfo or not relativeData then return CFrame.new() end

    -- Reconstruct relative CFrame
    local relativePosition = Vector3.new(
        relativeData.position.x,
        relativeData.position.y,
        relativeData.position.z
    )

    -- Reconstruct rotation from Euler angles
    local relativeCFrame = CFrame.new(relativePosition) * CFrame.Angles(
        relativeData.rotation.x,
        relativeData.rotation.y,
        relativeData.rotation.z
    )

    -- Convert back to world coordinates
    return plotInfo.cframe * relativeCFrame
end

-- Save all objects in a player's plot
function PlotObjectSaver.savePlayerObjects(player)
    local plots = workspace:FindFirstChild("Plots")
    if not plots then return false end
    
    -- Find player's plot
    local playerPlotFolder = nil
    for _, plotFolder in pairs(plots:GetChildren()) do
        local values = plotFolder:FindFirstChild("Values")
        local ownerId = values and values:FindFirstChild("OwnerId")
        
        if ownerId and ownerId.Value == player.UserId then
            playerPlotFolder = plotFolder
            break
        end
    end
    
    if not playerPlotFolder then return false end
    
    local plotInfo = getPlotInfo(playerPlotFolder)
    if not plotInfo then return false end
    
    -- Get objects folder in the plot
    local objectsFolder = playerPlotFolder:FindFirstChild("Objects")
    if not objectsFolder then return true end -- No objects to save
    
    local savedObjects = {}
    
    -- Save each object with relative position and rotation
    for _, object in pairs(objectsFolder:GetChildren()) do
        if object:IsA("Model") and object.PrimaryPart then
            local relativeData = worldToRelative(object:GetPrimaryPartCFrame(), plotInfo)

            -- Extract the base object name (remove timestamp suffix)
            local baseObjectName = object.Name:match("^(.-)_") or object.Name

            table.insert(savedObjects, {
                name = object.Name, -- Keep full name for reference
                position = relativeData.position,
                rotation = relativeData.rotation,
                objectType = baseObjectName -- Use base name for template lookup
            })
        end
    end
    
    -- Save to DataStore
    local success, errorMessage = pcall(function()
        plotObjectStore:SetAsync(player.UserId, savedObjects)
    end)
    
    if not success then
        warn("Failed to save plot objects for player", player.Name, ":", errorMessage)
        return false
    end
    
    print("Successfully saved", #savedObjects, "objects for player", player.Name)
    return true
end

-- Load and place objects in a player's plot
function PlotObjectSaver.loadPlayerObjects(player)
    local plots = workspace:FindFirstChild("Plots")
    if not plots then return false end
    
    -- Find player's plot
    local playerPlotFolder = nil
    for _, plotFolder in pairs(plots:GetChildren()) do
        local values = plotFolder:FindFirstChild("Values")
        local ownerId = values and values:FindFirstChild("OwnerId")
        
        if ownerId and ownerId.Value == player.UserId then
            playerPlotFolder = plotFolder
            break
        end
    end
    
    if not playerPlotFolder then return false end
    
    local plotInfo = getPlotInfo(playerPlotFolder)
    if not plotInfo then return false end
    
    -- Load saved objects from DataStore
    local savedObjects = nil
    local success, errorMessage = pcall(function()
        savedObjects = plotObjectStore:GetAsync(player.UserId)
    end)
    
    if not success then
        warn("Failed to load plot objects for player", player.Name, ":", errorMessage)
        return false
    end
    
    if not savedObjects or #savedObjects == 0 then
        print("No saved objects found for player", player.Name)
        return true
    end
    
    -- Ensure Objects folder exists
    local objectsFolder = playerPlotFolder:FindFirstChild("Objects")
    if not objectsFolder then
        objectsFolder = Instance.new("Folder")
        objectsFolder.Name = "Objects"
        objectsFolder.Parent = playerPlotFolder
    end
    
    -- Clear existing objects (in case of reload)
    for _, existingObject in pairs(objectsFolder:GetChildren()) do
        if existingObject:IsA("Model") then
            existingObject:Destroy()
        end
    end
    
    -- Load objects gradually in batches to prevent performance spikes
    PlotObjectSaver.loadObjectsGradually(player, savedObjects, plotInfo, objectsFolder)

    return true
end

-- Place a single object from saved data
function PlotObjectSaver.placeObjectFromData(player, objectData, plotInfo, objectsFolder)
    -- Get the object template from ReplicatedStorage
    local replicatedStorage = game:GetService("ReplicatedStorage")
    local objectsTemplateFolder = replicatedStorage:FindFirstChild("Objects")
    if not objectsTemplateFolder then return false end

    local objectTemplate = objectsTemplateFolder:FindFirstChild(objectData.objectType)
    if not objectTemplate or not objectTemplate:IsA("Model") then
        warn("Object template not found:", objectData.objectType)
        return false
    end

    -- Clone the object
    local newObject = objectTemplate:Clone()

    -- Generate a unique name with timestamp (same format as placement system)
    local timestamp = tostring(tick())
    newObject.Name = objectData.objectType .. "_" .. timestamp

    -- Calculate world position from relative data
    local worldCFrame = relativeToWorld(objectData, plotInfo)

    -- Set the object's position and rotation
    if newObject.PrimaryPart then
        newObject:SetPrimaryPartCFrame(worldCFrame)
    else
        warn("Object", newObject.Name, "has no PrimaryPart - cannot position accurately")
        newObject:MoveTo(worldCFrame.Position)
    end

    -- Parent to objects folder
    newObject.Parent = objectsFolder

    return true
end

-- Load objects gradually in batches to prevent performance spikes
function PlotObjectSaver.loadObjectsGradually(player, savedObjects, plotInfo, objectsFolder)
    if not savedObjects or #savedObjects == 0 then
        -- Still notify completion even with no objects
        local ReplicatedStorage = game:GetService("ReplicatedStorage")
        local plotObjectsLoadedEvent = ReplicatedStorage:FindFirstChild("Events"):FindFirstChild("PlotObjectsLoadedEvent")
        if plotObjectsLoadedEvent then
            plotObjectsLoadedEvent:FireClient(player)
        end
        return
    end

    -- Configuration for gradual loading
    local BATCH_SIZE = 4 -- Objects per batch (optimized for performance)
    local BATCH_DELAY = 0.08 -- Seconds between batches (fast but smooth)

    local objectsPlaced = 0
    local totalObjects = #savedObjects
    local currentBatch = 1

    -- Create batches
    local batches = {}
    for i = 1, totalObjects, BATCH_SIZE do
        local batch = {}
        for j = i, math.min(i + BATCH_SIZE - 1, totalObjects) do
            table.insert(batch, savedObjects[j])
        end
        table.insert(batches, batch)
    end

    -- Process batches gradually
    local function processBatch(batchIndex)
        if batchIndex > #batches then
            -- Notify client that loading is complete
            local ReplicatedStorage = game:GetService("ReplicatedStorage")
            local plotObjectsLoadedEvent = ReplicatedStorage:FindFirstChild("Events"):FindFirstChild("PlotObjectsLoadedEvent")
            if plotObjectsLoadedEvent then
                plotObjectsLoadedEvent:FireClient(player)
            end
            return
        end

        local batch = batches[batchIndex]

        -- Process objects in this batch with frame-by-frame loading
        local objectIndex = 1
        local function processNextObject()
            if objectIndex > #batch then
                -- Batch complete, schedule next batch
                task.wait(BATCH_DELAY)
                processBatch(batchIndex + 1)
                return
            end

            local objectData = batch[objectIndex]
            local success = PlotObjectSaver.placeObjectFromData(player, objectData, plotInfo, objectsFolder)
            if success then
                objectsPlaced = objectsPlaced + 1
            end

            -- Send progress update to client
            local ReplicatedStorage = game:GetService("ReplicatedStorage")
            local plotLoadingProgressEvent = ReplicatedStorage:FindFirstChild("Events"):FindFirstChild("PlotLoadingProgressEvent")
            if plotLoadingProgressEvent then
                local progress = objectsPlaced / totalObjects
                plotLoadingProgressEvent:FireClient(player, progress, objectsPlaced, totalObjects)
            end

            objectIndex = objectIndex + 1

            -- Process next object on next frame for smooth loading
            game:GetService("RunService").Heartbeat:Wait()
            processNextObject()
        end

        processNextObject()
    end

    -- Start the gradual loading process
    processBatch(1)
end

-- Clear all saved objects for a player (useful for plot reset)
function PlotObjectSaver.clearPlayerObjects(player)
    local success, errorMessage = pcall(function()
        plotObjectStore:RemoveAsync(player.UserId)
    end)

    if not success then
        warn("Failed to clear saved objects for player", player.Name, ":", errorMessage)
        return false
    end

    print("Cleared saved objects for player", player.Name)
    return true
end

return PlotObjectSaver
