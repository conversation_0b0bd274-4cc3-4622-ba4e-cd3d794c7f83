-- Example script showing how to use Plot Object Saver
-- This script demonstrates manual saving/loading and provides admin commands

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Wait for the PlotObjectSaver module to be available
local PlotObjectSaver
repeat
    PlotObjectSaver = _G.PlotObjectSaver
    if not PlotObjectSaver then
        wait(0.1)
    end
until PlotObjectSaver

print("Plot Object Saver Example Script Loaded!")

-- Admin commands for testing (only works for specific users)
local ADMIN_USERS = {"YourUsernameHere"} -- Replace with actual admin usernames

local function isAdmin(player)
    for _, adminName in pairs(ADMIN_USERS) do
        if player.Name == adminName then
            return true
        end
    end
    return false
end

-- Manual save command
local function savePlayerPlot(player, targetPlayerName)
    if not isAdmin(player) then
        return
    end
    
    local targetPlayer = targetPlayerName and Players:FindFirstChild(targetPlayerName) or player
    if not targetPlayer then
        print("Player not found:", targetPlayerName)
        return
    end
    
    local success = PlotObjectSaver.savePlayerObjects(targetPlayer)
    if success then
        print("Successfully saved plot objects for", targetPlayer.Name)
    else
        print("Failed to save plot objects for", targetPlayer.Name)
    end
end

-- Manual load command
local function loadPlayerPlot(player, targetPlayerName)
    if not isAdmin(player) then
        return
    end
    
    local targetPlayer = targetPlayerName and Players:FindFirstChild(targetPlayerName) or player
    if not targetPlayer then
        print("Player not found:", targetPlayerName)
        return
    end
    
    local success = PlotObjectSaver.loadPlayerObjects(targetPlayer)
    if success then
        print("Successfully loaded plot objects for", targetPlayer.Name)
    else
        print("Failed to load plot objects for", targetPlayer.Name)
    end
end

-- Clear saved objects command
local function clearPlayerPlot(player, targetPlayerName)
    if not isAdmin(player) then
        return
    end
    
    local targetPlayer = targetPlayerName and Players:FindFirstChild(targetPlayerName) or player
    if not targetPlayer then
        print("Player not found:", targetPlayerName)
        return
    end
    
    local success = PlotObjectSaver.clearPlayerObjects(targetPlayer)
    if success then
        print("Successfully cleared saved objects for", targetPlayer.Name)
    else
        print("Failed to clear saved objects for", targetPlayer.Name)
    end
end

-- Chat command handler
Players.PlayerAdded:Connect(function(player)
    player.Chatted:Connect(function(message)
        if not isAdmin(player) then return end
        
        local args = message:split(" ")
        local command = args[1]:lower()
        
        if command == "/saveplot" then
            local targetPlayer = args[2]
            savePlayerPlot(player, targetPlayer)
            
        elseif command == "/loadplot" then
            local targetPlayer = args[2]
            loadPlayerPlot(player, targetPlayer)
            
        elseif command == "/clearplot" then
            local targetPlayer = args[2]
            clearPlayerPlot(player, targetPlayer)
            
        elseif command == "/plothelp" then
            print("Plot Object Saver Commands:")
            print("/saveplot [playername] - Manually save plot objects")
            print("/loadplot [playername] - Manually load plot objects")
            print("/clearplot [playername] - Clear saved plot objects")
            print("If no playername is provided, commands apply to yourself")
        end
    end)
end)

-- Automatic saving every 5 minutes for all players (backup)
local function autoSaveAllPlots()
    print("Auto-saving all player plots...")
    local savedCount = 0
    
    for _, player in pairs(Players:GetPlayers()) do
        local success = PlotObjectSaver.savePlayerObjects(player)
        if success then
            savedCount = savedCount + 1
        end
    end
    
    print("Auto-save complete:", savedCount, "plots saved")
end

-- Start auto-save timer
spawn(function()
    while true do
        wait(300) -- 5 minutes
        autoSaveAllPlots()
    end
end)

-- Example function to demonstrate plot orientation handling
local function demonstratePlotOrientation()
    print("=== Plot Orientation Demo ===")
    print("The PlotObjectSaver automatically handles different plot orientations!")
    print("Objects are saved relative to their plot's coordinate system.")
    print("When a player selects a different plot (even if rotated differently),")
    print("their objects will be placed in the same relative positions and orientations.")
    print("This ensures consistency regardless of plot rotation or position.")
    print("=============================")
end

-- Run demonstration on script start
demonstratePlotOrientation()

-- Export functions for other scripts to use
_G.PlotObjectSaverExample = {
    savePlayerPlot = savePlayerPlot,
    loadPlayerPlot = loadPlayerPlot,
    clearPlayerPlot = clearPlayerPlot,
    autoSaveAllPlots = autoSaveAllPlots
}

print("Plot Object Saver Example Functions Available!")
print("Use /plothelp in chat (as admin) to see available commands")

-- Monitor plot selection events to show when objects are loaded
local plotSelectionEvent = ReplicatedStorage:WaitForChild("Events"):WaitForChild("PlotSelectionEvent")
plotSelectionEvent.OnServerEvent:Connect(function(player)
    print("Player", player.Name, "selected a plot - objects will be loaded automatically")
end)
