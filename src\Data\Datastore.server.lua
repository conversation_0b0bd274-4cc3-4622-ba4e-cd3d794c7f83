-- Roblox DataStore for saving/loading player money and object inventories, but only after plot selection
local DataStoreService = game:GetService("DataStoreService")
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Import modules
local ObjectInventory = require(ReplicatedStorage:WaitForChild("ObjectInventory"))
local EconomyAnalytics = require(script.Parent:WaitForChild("EconomyAnalytics"))
local PlotObjectSaver = require(script.Parent:WaitForChild("PlotObjectSaver"))

local moneyStore = DataStoreService:GetDataStore("PlayerMoney")
local objectStore = DataStoreService:GetDataStore("PlayerObjects")
local plotSelectionEvent = ReplicatedStorage:WaitForChild("Events"):WaitForChild("PlotSelectionEvent")

-- Create events for pre-loading data
local eventsFolder = ReplicatedStorage:WaitForChild("Events")
local preloadDataEvent = Instance.new("RemoteEvent")
preloadDataEvent.Name = "PreloadDataEvent"
preloadDataEvent.Parent = eventsFolder

local dataReadyEvent = Instance.new("RemoteEvent")
dataReadyEvent.Name = "DataReadyEvent"
dataReadyEvent.Parent = eventsFolder

-- Store pre-loaded data temporarily
local preloadedData = {}

local DEFAULT_MONEY = 0



-- Only create leaderstats/money after plot selection
local function loadMoney(player)
    local success, result = pcall(function()
        return moneyStore:GetAsync(player.UserId)
    end)
    if success and result ~= nil then
        return result
    else
        return DEFAULT_MONEY
    end
end

local function saveMoney(player, amount)
    pcall(function()
        moneyStore:SetAsync(player.UserId, amount)
    end)
end

-- Load player objects from datastore
local function loadObjects(player)
    local success, result = pcall(function()
        return objectStore:GetAsync(player.UserId)
    end)
    
    local defaultObjects = ObjectInventory.getDefaultObjects()
    
    if success and result ~= nil then
        -- Ensure all default objects exist in loaded data
        for objectName, defaultAmount in pairs(defaultObjects) do
            if result[objectName] == nil then
                result[objectName] = defaultAmount
            end
        end
        return result
    else
        -- Return default objects if load failed or no data exists
        return defaultObjects
    end
end

-- Save player objects to datastore
local function saveObjects(player, objects)
    pcall(function()
        objectStore:SetAsync(player.UserId, objects)
    end)
end

-- Make saveObjects available globally for ObjectInventory module
_G.saveObjects = saveObjects

-- Pre-load player data during loading screen (doesn't create leaderstats yet)
local function preloadPlayerData(player)
    print("Pre-loading data for player", player.Name)

    -- Load money and objects data but don't create leaderstats yet
    local playerMoney = loadMoney(player)
    local playerObjects = loadObjects(player)

    -- Store in temporary table
    preloadedData[player.UserId] = {
        money = playerMoney,
        objects = playerObjects
    }

    print("Data pre-loaded for", player.Name, "- Money:", playerMoney, "Objects:", #playerObjects)

    -- Notify client that data is ready
    dataReadyEvent:FireClient(player)
end

local function setupLeaderstats(player)
    if player:FindFirstChild("leaderstats") then return end

    -- Create leaderstats folder
    local leaderstats = Instance.new("Folder")
    leaderstats.Name = "leaderstats"
    leaderstats.Parent = player

    -- Setup money (use pre-loaded data if available)
    local money = Instance.new("IntValue")
    money.Name = "Money"

    local playerData = preloadedData[player.UserId]
    if playerData then
        money.Value = playerData.money
        print("Using pre-loaded money for", player.Name, ":", playerData.money)
    else
        money.Value = loadMoney(player)
        print("Loading money fresh for", player.Name)
    end

    money.Parent = leaderstats

    -- Track money changes and log analytics
    local lastMoneyValue = money.Value
    money.Changed:Connect(function(newValue)
        saveMoney(player, newValue)

        -- Log analytics for money changes using the EconomyAnalytics module
        EconomyAnalytics.LogMoneyChange(player, lastMoneyValue, newValue, {
            source = "unknown", -- Will be overridden by specific functions when called directly
            category = "unknown",
            item = "unknown"
        })

        lastMoneyValue = newValue
    end)
    
    -- Setup objects folder
    local objects = Instance.new("Folder")
    objects.Name = "Objects"
    objects.Parent = player

    -- Load and create object values (use pre-loaded data if available)
    local playerObjects
    local preloadedPlayerData = preloadedData[player.UserId]
    if preloadedPlayerData then
        playerObjects = preloadedPlayerData.objects
        print("Using pre-loaded objects for", player.Name, ":", #playerObjects, "types")
    else
        playerObjects = loadObjects(player)
        print("Loading objects fresh for", player.Name)
    end

    for objectName, amount in pairs(playerObjects) do
        local objectValue = Instance.new("IntValue")
        objectValue.Name = objectName
        objectValue.Value = amount
        objectValue.Parent = objects

        -- Save when object count changes
        objectValue.Changed:Connect(function()
            local currentObjects = {}
            for _, obj in pairs(objects:GetChildren()) do
                if obj:IsA("IntValue") then
                    currentObjects[obj.Name] = obj.Value
                end
            end
            saveObjects(player, currentObjects)
        end)
    end

    -- Clear pre-loaded data after use
    if preloadedData[player.UserId] then
        preloadedData[player.UserId] = nil
        print("Cleared pre-loaded data for", player.Name)
    end
end

-- Handle pre-loading data request from client
preloadDataEvent.OnServerEvent:Connect(function(player)
    preloadPlayerData(player)
end)

plotSelectionEvent.OnServerEvent:Connect(function(player)
    setupLeaderstats(player)

    -- Load saved objects after a brief delay to ensure plot is fully set up
    task.spawn(function()
        task.wait(1) -- Wait for plot setup to complete
        PlotObjectSaver.loadPlayerObjects(player)
    end)
end)

Players.PlayerRemoving:Connect(function(player)
    -- Save money
    local leaderstats = player:FindFirstChild("leaderstats")
    if leaderstats then
        local money = leaderstats:FindFirstChild("Money")
        if money then
            saveMoney(player, money.Value)
        end
    end

    -- Save objects
    local objects = player:FindFirstChild("Objects")
    if objects then
        local currentObjects = {}
        for _, obj in pairs(objects:GetChildren()) do
            if obj:IsA("IntValue") then
                currentObjects[obj.Name] = obj.Value
            end
        end
        saveObjects(player, currentObjects)
    end

    -- Save plot objects
    PlotObjectSaver.savePlayerObjects(player)
end)

game:BindToClose(function()
    for _, player in ipairs(Players:GetPlayers()) do
        -- Save money
        local leaderstats = player:FindFirstChild("leaderstats")
        if leaderstats then
            local money = leaderstats:FindFirstChild("Money")
            if money then
                saveMoney(player, money.Value)
            end
        end

        -- Save objects
        local objects = player:FindFirstChild("Objects")
        if objects then
            local currentObjects = {}
            for _, obj in pairs(objects:GetChildren()) do
                if obj:IsA("IntValue") then
                    currentObjects[obj.Name] = obj.Value
                end
            end
            saveObjects(player, currentObjects)
        end

        -- Save plot objects
        PlotObjectSaver.savePlayerObjects(player)
    end
end)

-- Make modules available globally for other scripts
_G.ObjectInventory = ObjectInventory
_G.EconomyAnalytics = EconomyAnalytics
_G.PlotObjectSaver = PlotObjectSaver
