-- Roblox DataStore for saving/loading player money and object inventories, but only after plot selection
local DataStoreService = game:GetService("DataStoreService")
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Import the ObjectInventory module
local ObjectInventory = require(ReplicatedStorage:WaitForChild("ObjectInventory"))

local moneyStore = DataStoreService:GetDataStore("PlayerMoney")
local objectStore = DataStoreService:GetDataStore("PlayerObjects")
local plotSelectionEvent = ReplicatedStorage:WaitForChild("Events"):WaitForChild("PlotSelectionEvent")

local DEFAULT_MONEY = 0

-- Only create leaderstats/money after plot selection
local function loadMoney(player)
    local success, result = pcall(function()
        return moneyStore:GetAsync(player.UserId)
    end)
    if success and result ~= nil then
        return result
    else
        return DEFAULT_MONEY
    end
end

local function saveMoney(player, amount)
    pcall(function()
        moneyStore:SetAsync(player.UserId, amount)
    end)
end

-- Load player objects from datastore
local function loadObjects(player)
    local success, result = pcall(function()
        return objectStore:GetAsync(player.UserId)
    end)
    
    local defaultObjects = ObjectInventory.getDefaultObjects()
    
    if success and result ~= nil then
        -- Ensure all default objects exist in loaded data
        for objectName, defaultAmount in pairs(defaultObjects) do
            if result[objectName] == nil then
                result[objectName] = defaultAmount
            end
        end
        return result
    else
        -- Return default objects if load failed or no data exists
        return defaultObjects
    end
end

-- Save player objects to datastore
local function saveObjects(player, objects)
    pcall(function()
        objectStore:SetAsync(player.UserId, objects)
    end)
end

-- Make saveObjects available globally for ObjectInventory module
_G.saveObjects = saveObjects

local function setupLeaderstats(player)
    if player:FindFirstChild("leaderstats") then return end
    
    -- Create leaderstats folder
    local leaderstats = Instance.new("Folder")
    leaderstats.Name = "leaderstats"
    leaderstats.Parent = player

    -- Setup money
    local money = Instance.new("IntValue")
    money.Name = "Money"
    money.Value = loadMoney(player)
    money.Parent = leaderstats

    money.Changed:Connect(function(newValue)
        saveMoney(player, newValue)
    end)
    
    -- Setup objects folder
    local objects = Instance.new("Folder")
    objects.Name = "Objects"
    objects.Parent = player
    
    -- Load and create object values
    local playerObjects = loadObjects(player)
    for objectName, amount in pairs(playerObjects) do
        local objectValue = Instance.new("IntValue")
        objectValue.Name = objectName
        objectValue.Value = amount
        objectValue.Parent = objects
        
        -- Save when object count changes
        objectValue.Changed:Connect(function()
            local currentObjects = {}
            for _, obj in pairs(objects:GetChildren()) do
                if obj:IsA("IntValue") then
                    currentObjects[obj.Name] = obj.Value
                end
            end
            saveObjects(player, currentObjects)
        end)
    end
end

plotSelectionEvent.OnServerEvent:Connect(function(player)
    setupLeaderstats(player)
end)

Players.PlayerRemoving:Connect(function(player)
    -- Save money
    local leaderstats = player:FindFirstChild("leaderstats")
    if leaderstats then
        local money = leaderstats:FindFirstChild("Money")
        if money then
            saveMoney(player, money.Value)
        end
    end
    
    -- Save objects
    local objects = player:FindFirstChild("Objects")
    if objects then
        local currentObjects = {}
        for _, obj in pairs(objects:GetChildren()) do
            if obj:IsA("IntValue") then
                currentObjects[obj.Name] = obj.Value
            end
        end
        saveObjects(player, currentObjects)
    end
end)

game:BindToClose(function()
    for _, player in ipairs(Players:GetPlayers()) do
        -- Save money
        local leaderstats = player:FindFirstChild("leaderstats")
        if leaderstats then
            local money = leaderstats:FindFirstChild("Money")
            if money then
                saveMoney(player, money.Value)
            end
        end
        
        -- Save objects
        local objects = player:FindFirstChild("Objects")
        if objects then
            local currentObjects = {}
            for _, obj in pairs(objects:GetChildren()) do
                if obj:IsA("IntValue") then
                    currentObjects[obj.Name] = obj.Value
                end
            end
            saveObjects(player, currentObjects)
        end
    end
end)

-- Make ObjectInventory module available globally for other scripts
_G.ObjectInventory = ObjectInventory
